<?php $__env->startComponent('mail::message'); ?>
<?php echo e(__('You have been invited to join the :company company!', ['company' => $invitation->company->name])); ?>


<?php if(filament()->getRegistrationUrl()): ?>
<?php echo e(__('If you do not have an account, you may create one by clicking the button below. After creating an account, you may click the invitation acceptance button in this email to accept the company invitation:')); ?>


<?php $__env->startComponent('mail::button', ['url' => url(filament()->getRegistrationUrl())]); ?>
    <?php echo e(__('Create Account')); ?>

<?php echo $__env->renderComponent(); ?>

<?php echo e(__('If you already have an account, you may accept this invitation by clicking the button below:')); ?>


<?php else: ?>
<?php echo e(__('You may accept this invitation by clicking the button below:')); ?>

<?php endif; ?>


<?php $__env->startComponent('mail::button', ['url' => $acceptUrl]); ?>
<?php echo e(__('Accept Invitation')); ?>

<?php echo $__env->renderComponent(); ?>

<?php echo e(__('If you did not expect to receive an invitation to this company, you may discard this email.')); ?>

<?php echo $__env->renderComponent(); ?>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\vendor\andrewdwallo\filament-companies\resources\views\mail\company-invitation.blade.php ENDPATH**/ ?>