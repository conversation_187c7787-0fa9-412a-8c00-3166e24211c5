<?php

namespace App\Filament\Company\Resources\ArenaDoviz\ClientBalanceResource\Pages;

use App\Filament\Company\Resources\ArenaDoviz\ClientBalanceResource;
use App\Models\ArenaDoviz\ClientBalance;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateClientBalance extends CreateRecord
{
    protected static string $resource = ClientBalanceResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        // Use updateOrCreate to handle unique constraint violations
        return ClientBalance::updateOrCreate(
            [
                'company_id' => $data['company_id'] ?? auth()->user()->currentCompany->id,
                'client_id' => $data['client_id'],
                'currency_code' => $data['currency_code'],
            ],
            $data
        );
    }
}
