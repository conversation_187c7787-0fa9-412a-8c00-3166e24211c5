<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['customer']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['customer']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<div class="space-y-6">
    <!-- Customer Profile Header -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Customer Profile: <?php echo e($customer->name ?? 'John Doe'); ?></h3>
            <div class="flex space-x-2">
                <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    Edit
                </button>
                <button type="button" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                    Delete
                </button>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Photo Section -->
            <div class="lg:col-span-1">
                <div class="w-32 h-32 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                    <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                <p class="text-sm text-gray-500 mt-2 text-center">[Photo]</p>
            </div>

            <!-- Personal Information -->
            <div class="lg:col-span-3">
                <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">Personal Information</h4>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Name</label>
                        <p class="text-sm text-gray-900 dark:text-white"><?php echo e($customer->name ?? 'John Doe'); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Phone</label>
                        <p class="text-sm text-gray-900 dark:text-white"><?php echo e($customer->phone ?? '+90 ************'); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Company</label>
                        <p class="text-sm text-gray-900 dark:text-white"><?php echo e($customer->company ?? 'ABC Trading Ltd.'); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">WhatsApp Group</label>
                        <p class="text-sm text-gray-900 dark:text-white"><?php echo e($customer->whatsapp_group ?? '#group_001'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Balances -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">Current Balances</h4>
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 p-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                <div>
                    <div class="text-lg font-bold text-blue-600 dark:text-blue-400">$2,500.00</div>
                    <div class="text-sm text-blue-700 dark:text-blue-300">USD</div>
                </div>
                <div>
                    <div class="text-lg font-bold text-blue-600 dark:text-blue-400">€1,200.00</div>
                    <div class="text-sm text-blue-700 dark:text-blue-300">EUR</div>
                </div>
                <div>
                    <div class="text-lg font-bold text-blue-600 dark:text-blue-400">₺15,000.00</div>
                    <div class="text-sm text-blue-700 dark:text-blue-300">TRY</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-4">
            <h4 class="text-md font-semibold text-gray-900 dark:text-white">Recent Transactions</h4>
            <button type="button" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium">
                View All
            </button>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full text-sm">
                <thead>
                    <tr class="border-b border-gray-200 dark:border-gray-700">
                        <th class="text-left py-2 font-medium text-gray-900 dark:text-white">Date</th>
                        <th class="text-left py-2 font-medium text-gray-900 dark:text-white">Type</th>
                        <th class="text-left py-2 font-medium text-gray-900 dark:text-white">Amount</th>
                        <th class="text-left py-2 font-medium text-gray-900 dark:text-white">Status</th>
                        <th class="text-left py-2 font-medium text-gray-900 dark:text-white">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="border-b border-gray-100 dark:border-gray-700">
                        <td class="py-2 text-gray-700 dark:text-gray-300">15/01/25</td>
                        <td class="py-2 text-gray-700 dark:text-gray-300">Buy</td>
                        <td class="py-2 text-gray-900 dark:text-white font-mono">$1,000</td>
                        <td class="py-2">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                Completed
                            </span>
                        </td>
                        <td class="py-2">
                            <button type="button" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm">
                                View
                            </button>
                        </td>
                    </tr>
                    <tr class="border-b border-gray-100 dark:border-gray-700">
                        <td class="py-2 text-gray-700 dark:text-gray-300">14/01/25</td>
                        <td class="py-2 text-gray-700 dark:text-gray-300">Sell</td>
                        <td class="py-2 text-gray-900 dark:text-white font-mono">€500</td>
                        <td class="py-2">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                Completed
                            </span>
                        </td>
                        <td class="py-2">
                            <button type="button" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm">
                                View
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Notes -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">Notes</h4>
        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <p class="text-sm text-gray-700 dark:text-gray-300">
                <?php echo e($customer->notes ?? 'Regular customer, prefers USD transactions...'); ?>

            </p>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\arena-doviz\customer-profile.blade.php ENDPATH**/ ?>