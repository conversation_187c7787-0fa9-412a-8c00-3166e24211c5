<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'icon' => null,
    'currentTenant' => null,
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'icon' => null,
    'currentTenant' => null,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<?php
    $currentTenantName = filament()->getTenantName($currentTenant);
    $currentCompany = auth()->user()->currentCompany;
    $currentCompanyOwner = $currentCompany->owner;
    $items = filament()->getTenantMenuItems();
    $profileItem = $items['profile'] ?? null;
    $profileItemUrl = $profileItem?->getUrl();
?>

<li class="grid grid-flow-col auto-cols-max gap-x-2 items-start p-2">
    <div class="icon h-9 w-9 flex items-center justify-center rounded-full bg-gray-200 dark:bg-white/10">
        <?php if (isset($component)) { $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.icon','data' => ['icon' => $icon,'class' => 'h-6 w-6 text-gray-600 dark:text-gray-200']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($icon),'class' => 'h-6 w-6 text-gray-600 dark:text-gray-200']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $attributes = $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $component = $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
    </div>
    <div>
        <div class="px-2 pb-2">
            <h2 class="text-gray-800 dark:text-gray-200 text-base font-semibold">
                <?php echo e($currentTenantName); ?>

            </h2>
            <p class="text-sm font-normal text-gray-500 dark:text-gray-400">
                <?php echo e($currentCompanyOwner->email); ?>

            </p>
        </div>
    </div>
</li>

<?php if (isset($component)) { $__componentOriginal2677d6ad3067c4282b32a41c21dedc6d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.panel-shift-dropdown.item','data' => ['url' => \App\Filament\Company\Clusters\Settings::getUrl(),'label' => 'All Settings','icon' => 'heroicon-m-cog-6-tooth']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('panel-shift-dropdown.item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['url' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(\App\Filament\Company\Clusters\Settings::getUrl()),'label' => 'All Settings','icon' => 'heroicon-m-cog-6-tooth']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d)): ?>
<?php $attributes = $__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d; ?>
<?php unset($__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2677d6ad3067c4282b32a41c21dedc6d)): ?>
<?php $component = $__componentOriginal2677d6ad3067c4282b32a41c21dedc6d; ?>
<?php unset($__componentOriginal2677d6ad3067c4282b32a41c21dedc6d); ?>
<?php endif; ?>

<?php if (isset($component)) { $__componentOriginal2677d6ad3067c4282b32a41c21dedc6d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.panel-shift-dropdown.item','data' => ['url' => $profileItemUrl ?? filament()->getTenantProfileUrl(),'label' => $profileItem?->getLabel() ?? filament()->getTenantProfilePage()::getLabel(),'icon' => 'heroicon-m-briefcase']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('panel-shift-dropdown.item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['url' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($profileItemUrl ?? filament()->getTenantProfileUrl()),'label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($profileItem?->getLabel() ?? filament()->getTenantProfilePage()::getLabel()),'icon' => 'heroicon-m-briefcase']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d)): ?>
<?php $attributes = $__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d; ?>
<?php unset($__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2677d6ad3067c4282b32a41c21dedc6d)): ?>
<?php $component = $__componentOriginal2677d6ad3067c4282b32a41c21dedc6d; ?>
<?php unset($__componentOriginal2677d6ad3067c4282b32a41c21dedc6d); ?>
<?php endif; ?>

<?php if (isset($component)) { $__componentOriginal4e2351c219bf8efe995b383efaa9f871 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4e2351c219bf8efe995b383efaa9f871 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.panel-shift-dropdown.toggle','data' => ['label' => 'Switch Company','icon' => 'heroicon-m-arrows-right-left','panelId' => 'company-switcher']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('panel-shift-dropdown.toggle'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Switch Company','icon' => 'heroicon-m-arrows-right-left','panel-id' => 'company-switcher']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4e2351c219bf8efe995b383efaa9f871)): ?>
<?php $attributes = $__attributesOriginal4e2351c219bf8efe995b383efaa9f871; ?>
<?php unset($__attributesOriginal4e2351c219bf8efe995b383efaa9f871); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4e2351c219bf8efe995b383efaa9f871)): ?>
<?php $component = $__componentOriginal4e2351c219bf8efe995b383efaa9f871; ?>
<?php unset($__componentOriginal4e2351c219bf8efe995b383efaa9f871); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\panel-shift-dropdown\company-settings.blade.php ENDPATH**/ ?>