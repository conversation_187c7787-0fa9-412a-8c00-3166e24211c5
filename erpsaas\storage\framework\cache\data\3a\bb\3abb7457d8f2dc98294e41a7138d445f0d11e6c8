1756292127O:54:"App\Transformers\EntityBalanceSummaryReportTransformer":2:{s:9:" * report";O:17:"App\DTO\ReportDTO":10:{s:10:"categories";a:1:{s:8:"Entities";a:0:{}}s:12:"overallTotal";N;s:12:"agingSummary";N;s:18:"entityBalanceTotal";O:24:"App\DTO\EntityBalanceDTO":4:{s:12:"totalBalance";s:6:"A$0.00";s:11:"paidBalance";s:6:"A$0.00";s:13:"unpaidBalance";s:6:"A$0.00";s:14:"overdueBalance";N;}s:21:"overallPaymentMetrics";N;s:6:"fields";a:4:{i:0;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:0;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";E:37:"Filament\Support\Enums\Alignment:Left";s:8:" * label";s:6:"Vendor";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:11:"entity_name";}i:1;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:1;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";E:38:"Filament\Support\Enums\Alignment:Right";s:8:" * label";s:5:"Total";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:13:"total_balance";}i:2;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:1;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";r:34;s:8:" * label";s:4:"Paid";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:12:"paid_balance";}i:3;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:1;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";r:34;s:8:" * label";s:6:"Unpaid";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:14:"unpaid_balance";}}s:10:"reportType";N;s:8:"overview";N;s:9:"startDate";O:25:"Illuminate\Support\Carbon":3:{s:4:"date";s:26:"2025-01-01 00:00:00.000000";s:13:"timezone_type";i:3;s:8:"timezone";s:3:"UTC";}s:7:"endDate";O:25:"Illuminate\Support\Carbon":3:{s:4:"date";s:26:"2025-08-27 23:59:59.000000";s:13:"timezone_type";i:3;s:8:"timezone";s:3:"UTC";}}s:66:" App\Transformers\EntityBalanceSummaryReportTransformer entityType";E:46:"App\Enums\Accounting\DocumentEntityType:Vendor";}