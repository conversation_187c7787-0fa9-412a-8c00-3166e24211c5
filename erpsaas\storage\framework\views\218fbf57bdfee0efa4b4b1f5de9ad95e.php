<?php
    $data = $this->form->getRawState();
    $document = \App\DTO\DocumentPreviewDTO::fromSettings($this->record, $data);
    $template = $getTemplate();
    $preview = $isPreview();
?>

<?php echo $document->getFontHtml(); ?>


<style>
    .doc-template-paper {
        font-family: '<?php echo e($document->font->getLabel()); ?>', sans-serif;
    }
</style>

<div <?php echo e($attributes); ?>>
    <?php echo $__env->make("filament.company.components.document-templates.{$template->value}", [
        'document' => $document,
        'preview' => $preview,
    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</div>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\filament\forms\components\document-preview.blade.php ENDPATH**/ ?>