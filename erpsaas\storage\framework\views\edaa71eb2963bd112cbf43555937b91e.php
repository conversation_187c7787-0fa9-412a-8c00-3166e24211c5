<?php $__env->startSection('content'); ?>
    <div class="header">
        <div class="title"><?php echo e($report->getTitle()); ?></div>
        <div class="company-name"><?php echo e($company->name); ?></div>
        <?php if($startDate && $endDate): ?>
            <div class="date-range">Date Range: <?php echo e($startDate); ?> to <?php echo e($endDate); ?></div>
        <?php else: ?>
            <div class="date-range">As of <?php echo e($endDate); ?></div>
        <?php endif; ?>
    </div>
    <table class="table-class">
        <thead class="table-head">
        <tr>
            <?php $__currentLoopData = $report->getHeaders(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $header): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <th class="<?php echo e($report->getAlignmentClass($index)); ?>">
                    <?php echo e($header); ?>

                </th>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tr>
        </thead>
        <?php $__currentLoopData = $report->getCategories(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tbody>
            <?php if(! empty($category->header)): ?>
                <tr class="category-header-row">
                    <?php $__currentLoopData = $category->header; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $header): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <td class="<?php echo e($report->getAlignmentClass($index)); ?>">
                            <?php echo e($header); ?>

                        </td>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tr>
            <?php endif; ?>
            <?php $__currentLoopData = $category->data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <?php $__currentLoopData = $account; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $cell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <td class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                            $report->getAlignmentClass($index),
                            'whitespace-normal' => $index === 'account_name',
                            'whitespace-nowrap' => $index !== 'account_name',
                        ]); ?>"
                        >
                            <?php if(is_array($cell) && isset($cell['name'])): ?>
                                <?php echo e($cell['name']); ?>

                            <?php else: ?>
                                <?php echo e($cell); ?>

                            <?php endif; ?>
                        </td>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            <!-- Category Types -->
            <?php $__currentLoopData = $category->types ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <!-- Type Header -->
                <tr class="type-header-row">
                    <?php $__currentLoopData = $type->header; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $header): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <td class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                            $report->getAlignmentClass($index),
                            'type-row-indent' => $index === 'account_name',
                        ]); ?>"
                        >
                            <?php echo e($header); ?>

                        </td>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tr>

                <!-- Type Data -->
                <?php $__currentLoopData = $type->data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $typeRow): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr class="type-data-row">
                        <?php $__currentLoopData = $typeRow; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $cell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <td class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                                $report->getAlignmentClass($index),
                                'whitespace-normal type-row-indent' => $index === 'account_name',
                                'whitespace-nowrap' => $index !== 'account_name',
                            ]); ?>"
                            >
                                <?php if(is_array($cell) && isset($cell['name'])): ?>
                                    <?php echo e($cell['name']); ?>

                                <?php else: ?>
                                    <?php echo e($cell); ?>

                                <?php endif; ?>
                            </td>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                <!-- Type Summary -->
                <tr class="type-summary-row">
                    <?php $__currentLoopData = $type->summary; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $cell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <td class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                            $report->getAlignmentClass($index),
                            'type-row-indent' => $index === 'account_name',
                        ]); ?>"
                        >
                            <?php echo e($cell); ?>

                        </td>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            <?php if(! empty($category->summary)): ?>
                <tr class="category-summary-row">
                    <?php $__currentLoopData = $category->summary; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $cell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <td class="<?php echo e($report->getAlignmentClass($index)); ?>">
                            <?php echo e($cell); ?>

                        </td>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tr>

                <?php if (! ($loop->last && empty($report->getOverallTotals()))): ?>
                    <tr class="spacer-row">
                        <td colspan="<?php echo e(count($report->getHeaders())); ?>"></td>
                    </tr>
                <?php endif; ?>
            <?php endif; ?>
            </tbody>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <tfoot>
        <tr class="table-footer-row">
            <?php $__currentLoopData = $report->getOverallTotals(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $total): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <td class="<?php echo e($report->getAlignmentClass($index)); ?>">
                    <?php echo e($total); ?>

                </td>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tr>
        </tfoot>
    </table>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('components.company.reports.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\company\reports\report-pdf.blade.php ENDPATH**/ ?>