<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['provider', 'createdAt' => null]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['provider', 'createdAt' => null]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<?php
    $providerEnum = \Wallo\FilamentCompanies\Enums\Provider::tryFrom($provider);
?>

<?php if($providerEnum?->isEnabled()): ?>
    <div class="filament-companies-connected-account">
        <div class="filament-companies-connected-account-container flex items-center justify-between">
            <div class="filament-companies-connected-account-details flex items-center gap-x-2">
                <div class="filament-companies-connected-account-icon h-8 w-8">
                    <?php echo e($providerEnum->getIconView()); ?>

                </div>

                <div class="filament-companies-connected-account-info font-semibold">
                    <div class="filament-companies-connected-account-name text-sm text-gray-800 dark:text-gray-200">
                        <?php echo e($providerEnum->getLabel()); ?>

                    </div>

                    <?php if(!empty($createdAt)): ?>
                        <div
                            class="filament-companies-connected-account-connected text-xs text-primary-700 dark:text-primary-500">
                            <?php echo e(__('filament-companies::default.labels.connected')); ?>

                            <div
                                class="filament-companies-connected-account-connected-date text-xs text-gray-600 dark:text-gray-300">
                                <?php echo e($createdAt); ?>

                            </div>
                        </div>
                    <?php else: ?>
                        <div class="filament-companies-connected-account-not-connected text-xs text-gray-400">
                            <?php echo e(__('filament-companies::default.labels.not_connected')); ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div>
                <?php echo e($action); ?>

            </div>
        </div>

        <?php $__errorArgs = [$provider.'_connect_error'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
        <div class="filament-companies-connected-account-error text-sm font-semibold text-danger-500 px-3 mt-2">
            <?php echo e($message); ?>

        </div>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\vendor\andrewdwallo\filament-companies\resources\views\components\connected-account.blade.php ENDPATH**/ ?>