<?php

namespace App\Filament\Company\Widgets;

use App\Services\ArenaDoviz\DashboardAnalyticsService;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Support\Number;

class TopClientsWidget extends BaseWidget
{
    protected static ?string $heading = 'Top Clients by Volume';
    protected static ?int $sort = 4;
    protected int | string | array $columnSpan = 'full';
    protected static ?string $pollingInterval = '60s';

    private function getAnalyticsService(): DashboardAnalyticsService
    {
        return app(DashboardAnalyticsService::class);
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Client Name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('email')
                    ->label('Email')
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('transaction_count')
                    ->label('Transactions')
                    ->numeric()
                    ->sortable()
                    ->alignCenter()
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('total_volume')
                    ->label('Total Volume')
                    ->money('TRY')
                    ->sortable()
                    ->alignEnd()
                    ->weight('bold')
                    ->color('success'),

                Tables\Columns\TextColumn::make('total_commission')
                    ->label('Commission')
                    ->money('TRY')
                    ->sortable()
                    ->alignEnd()
                    ->color('warning'),

                Tables\Columns\TextColumn::make('avg_transaction')
                    ->label('Avg Transaction')
                    ->money('TRY')
                    ->sortable()
                    ->alignEnd()
                    ->toggleable(),
            ])
            ->defaultSort('total_volume', 'desc')
            ->paginated(false)
            ->striped()
            ->emptyStateHeading('No client data available')
            ->emptyStateDescription('Start processing transactions to see client analytics.')
            ->emptyStateIcon('heroicon-o-users');
    }

    protected function getTableQuery(): \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Relations\Relation|null
    {
        try {
            $company = auth()->user()->currentCompany;
            $startDate = now()->startOfMonth();
            $endDate = now();

            $metrics = $this->getAnalyticsService()->getDashboardMetrics($company, $startDate, $endDate);
            $topClients = $metrics['clients']['top_clients'] ?? collect();

            if ($topClients->isEmpty()) {
                // Return empty query if no data
                return \App\Models\Common\Client::query()->whereRaw('1 = 0');
            }

            // Create a fake query builder that returns our processed data
            // We'll use the Client model as base but override the results
            $query = \App\Models\Common\Client::query()
                ->where('company_id', $company->id)
                ->limit(10);

            // Store the processed data for use in getTableRecords
            $this->cachedTopClients = collect($topClients)->map(function ($client) {
                return (object) [
                    'id' => $client->client_id ?? 0,
                    'name' => $client->client->name ?? 'Unknown Client',
                    'email' => $client->client->primaryContact->email ?? 'N/A',
                    'transaction_count' => $client->transaction_count ?? 0,
                    'total_volume' => $client->total_volume ?? 0,
                    'total_commission' => $client->total_commission ?? 0,
                    'avg_transaction' => ($client->transaction_count ?? 0) > 0 ? ($client->total_volume ?? 0) / ($client->transaction_count ?? 1) : 0,
                ];
            });

            return $query;
        } catch (\Exception $e) {
            // Log error and return empty query
            logger()->error('TopClientsWidget error: ' . $e->getMessage());
            return \App\Models\Common\Client::query()->whereRaw('1 = 0');
        }
    }

    protected $cachedTopClients;

    public function getTableRecords(): \Illuminate\Database\Eloquent\Collection|\Illuminate\Contracts\Pagination\Paginator|\Illuminate\Contracts\Pagination\CursorPaginator
    {
        return $this->cachedTopClients ?? collect();
    }
}
