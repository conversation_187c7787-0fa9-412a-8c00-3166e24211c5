<?php
    use App\Enums\Accounting\DocumentDiscountMethod;
    use App\Utilities\Currency\CurrencyAccessor;
    use App\View\Models\DocumentTotalViewModel;

    $data = $this->form->getRawState();
    $type = $getType();
    $viewModel = new DocumentTotalViewModel($data, $type);
    extract($viewModel->buildViewData(), EXTR_SKIP);
?>

<div class="totals-summary w-full lg:pl-[4rem] lg:pr-[6rem] py-8 lg:py-0">
    <table class="w-full text-right table-fixed hidden lg:table">
        <colgroup>
            <col class="w-[30%]"> 
            <col class="w-[10%]"> 
            <col class="w-[10%]"> 
            <col class="w-[15%]">
            <col class="w-[15%]"> 
            <col class="w-[10%]"> 
        </colgroup>
        <tbody>
        <?php if($subtotal): ?>
            <tr>
                <td colspan="4"></td>
                <td class="text-sm p-2 font-semibold text-gray-950 dark:text-white">Subtotal:</td>
                <td class="text-sm p-2"><?php echo e($subtotal); ?></td>
            </tr>
        <?php endif; ?>
        <?php if($taxTotal): ?>
            <tr>
                <td colspan="4"></td>
                <td class="text-sm p-2">Tax:</td>
                <td class="text-sm p-2"><?php echo e($taxTotal); ?></td>
            </tr>
        <?php endif; ?>
        <?php if($isPerDocumentDiscount): ?>
            <tr>
                <td colspan="3" class="text-sm p-2">Discount:</td>
                <td colspan="2" class="text-sm p-2">
                    <div class="flex justify-between space-x-2">
                        <?php $__currentLoopData = $getChildComponentContainer()->getComponents(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $component): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex-1 text-left"><?php echo e($component); ?></div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </td>
                <td class="text-sm p-2">(<?php echo e($discountTotal); ?>)</td>
            </tr>
        <?php elseif($discountTotal): ?>
            <tr>
                <td colspan="4"></td>
                <td class="text-sm p-2">Discount:</td>
                <td class="text-sm p-2">(<?php echo e($discountTotal); ?>)</td>
            </tr>
        <?php endif; ?>
        <tr>
            <td colspan="4"></td>
            <td class="text-sm p-2 font-semibold text-gray-950 dark:text-white"><?php echo e($amountDue ? 'Total' : 'Grand Total'); ?>:</td>
            <td class="text-sm p-2"><?php echo e($grandTotal); ?></td>
        </tr>
        <?php if($amountDue): ?>
            <tr>
                <td colspan="4"></td>
                <td class="text-sm p-2 font-semibold text-gray-950 dark:text-white border-t-4 border-double">Amount Due
                    (<?php echo e($currencyCode); ?>):
                </td>
                <td class="text-sm p-2 border-t-4 border-double"><?php echo e($amountDue); ?></td>
            </tr>
        <?php endif; ?>
        <?php if($conversionMessage): ?>
            <tr>
                <td colspan="6" class="text-sm p-2 text-gray-600">
                    <?php echo e($conversionMessage); ?>

                </td>
            </tr>
        <?php endif; ?>
        </tbody>
    </table>

    <!-- Mobile View -->
    <div class="block lg:hidden">
        <div class="flex flex-col space-y-6">
            <?php if($subtotal): ?>
                <div class="flex justify-between items-center">
                    <span class="text-sm font-semibold text-gray-950 dark:text-white">Subtotal:</span>
                    <span class="text-sm"><?php echo e($subtotal); ?></span>
                </div>
            <?php endif; ?>
            <?php if($taxTotal): ?>
                <div class="flex justify-between items-center">
                    <span class="text-sm">Tax:</span>
                    <span class="text-sm"><?php echo e($taxTotal); ?></span>
                </div>
            <?php endif; ?>
            <?php if($isPerDocumentDiscount): ?>
                <div class="flex flex-col space-y-2">
                    <span class="text-sm">Discount:</span>
                    <div class="flex justify-between space-x-2">
                        <?php $__currentLoopData = $getChildComponentContainer()->getComponents(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $component): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="w-1/2"><?php echo e($component); ?></div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php elseif($discountTotal): ?>
                <div class="flex justify-between items-center">
                    <span class="text-sm">Discount:</span>
                    <span class="text-sm">(<?php echo e($discountTotal); ?>)</span>
                </div>
            <?php endif; ?>
            <div class="flex justify-between items-center">
                <span class="text-sm font-semibold text-gray-950 dark:text-white"><?php echo e($amountDue ? 'Total' : 'Grand Total'); ?>:</span>
                <span class="text-sm"><?php echo e($grandTotal); ?></span>
            </div>
            <?php if($amountDue): ?>
                <div class="flex justify-between items-center">
                    <span
                        class="text-sm font-semibold text-gray-950 dark:text-white">Amount Due (<?php echo e($currencyCode); ?>):</span>
                    <span class="text-sm"><?php echo e($amountDue); ?></span>
                </div>
            <?php endif; ?>
            <?php if($conversionMessage): ?>
                <div class="text-sm text-gray-600">
                    <?php echo e($conversionMessage); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\filament\forms\components\document-totals.blade.php ENDPATH**/ ?>