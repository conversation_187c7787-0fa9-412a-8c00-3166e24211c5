<?php return array (
  'broadcasting' => 
  array (
    'default' => 'log',
    'connections' => 
    array (
      'reverb' => 
      array (
        'driver' => 'reverb',
        'key' => NULL,
        'secret' => NULL,
        'app_id' => NULL,
        'options' => 
        array (
          'host' => NULL,
          'port' => 443,
          'scheme' => 'https',
          'useTLS' => true,
        ),
        'client_options' => 
        array (
        ),
      ),
      'pusher' => 
      array (
        'driver' => 'pusher',
        'key' => NULL,
        'secret' => NULL,
        'app_id' => NULL,
        'options' => 
        array (
          'cluster' => NULL,
          'host' => 'api-mt1.pusher.com',
          'port' => 443,
          'scheme' => 'https',
          'encrypted' => true,
          'useTLS' => true,
        ),
        'client_options' => 
        array (
        ),
      ),
      'ably' => 
      array (
        'driver' => 'ably',
        'key' => NULL,
      ),
      'log' => 
      array (
        'driver' => 'log',
      ),
      'null' => 
      array (
        'driver' => 'null',
      ),
    ),
  ),
  'concurrency' => 
  array (
    'default' => 'process',
  ),
  'cors' => 
  array (
    'paths' => 
    array (
      0 => 'api/*',
      1 => 'sanctum/csrf-cookie',
    ),
    'allowed_methods' => 
    array (
      0 => '*',
    ),
    'allowed_origins' => 
    array (
      0 => '*',
    ),
    'allowed_origins_patterns' => 
    array (
    ),
    'allowed_headers' => 
    array (
      0 => '*',
    ),
    'exposed_headers' => 
    array (
    ),
    'max_age' => 0,
    'supports_credentials' => false,
  ),
  'hashing' => 
  array (
    'driver' => 'bcrypt',
    'bcrypt' => 
    array (
      'rounds' => '12',
      'verify' => true,
      'limit' => NULL,
    ),
    'argon' => 
    array (
      'memory' => 65536,
      'threads' => 1,
      'time' => 4,
      'verify' => true,
    ),
    'rehash_on_login' => true,
  ),
  'view' => 
  array (
    'paths' => 
    array (
      0 => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\resources\\views',
    ),
    'compiled' => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\storage\\framework\\views',
  ),
  'app' => 
  array (
    'name' => 'Arena Doviz',
    'env' => 'local',
    'debug' => true,
    'url' => 'http://localhost:8000',
    'frontend_url' => 'http://localhost:3000',
    'asset_url' => NULL,
    'timezone' => 'UTC',
    'locale' => 'en',
    'fallback_locale' => 'en',
    'faker_locale' => 'en_US',
    'cipher' => 'AES-256-CBC',
    'key' => 'base64:IH526ioRiGsYmZQuplSZxl0fJtAetl2rGUiVQUXyac4=',
    'previous_keys' => 
    array (
    ),
    'maintenance' => 
    array (
      'driver' => 'file',
      'store' => 'database',
    ),
    'providers' => 
    array (
      0 => 'Illuminate\\Auth\\AuthServiceProvider',
      1 => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
      2 => 'Illuminate\\Bus\\BusServiceProvider',
      3 => 'Illuminate\\Cache\\CacheServiceProvider',
      4 => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
      5 => 'Illuminate\\Concurrency\\ConcurrencyServiceProvider',
      6 => 'Illuminate\\Cookie\\CookieServiceProvider',
      7 => 'Illuminate\\Database\\DatabaseServiceProvider',
      8 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
      9 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
      10 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
      11 => 'Illuminate\\Hashing\\HashServiceProvider',
      12 => 'Illuminate\\Mail\\MailServiceProvider',
      13 => 'Illuminate\\Notifications\\NotificationServiceProvider',
      14 => 'Illuminate\\Pagination\\PaginationServiceProvider',
      15 => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
      16 => 'Illuminate\\Pipeline\\PipelineServiceProvider',
      17 => 'Illuminate\\Queue\\QueueServiceProvider',
      18 => 'Illuminate\\Redis\\RedisServiceProvider',
      19 => 'Illuminate\\Session\\SessionServiceProvider',
      20 => 'Illuminate\\Translation\\TranslationServiceProvider',
      21 => 'Illuminate\\Validation\\ValidationServiceProvider',
      22 => 'Illuminate\\View\\ViewServiceProvider',
      23 => 'App\\Providers\\NumberFormattingServiceProvider',
      24 => 'App\\Providers\\AppServiceProvider',
      25 => 'App\\Providers\\Filament\\CompanyPanelProvider',
      26 => 'App\\Providers\\Filament\\UserPanelProvider',
      27 => 'App\\Providers\\Faker\\FakerServiceProvider',
      28 => 'App\\Providers\\MacroServiceProvider',
      29 => 'App\\Providers\\SquireServiceProvider',
      30 => 'App\\Providers\\TranslationServiceProvider',
      31 => 'App\\Providers\\CurrencyServiceProvider',
    ),
    'aliases' => 
    array (
      'App' => 'Illuminate\\Support\\Facades\\App',
      'Arr' => 'Illuminate\\Support\\Arr',
      'Artisan' => 'Illuminate\\Support\\Facades\\Artisan',
      'Auth' => 'Illuminate\\Support\\Facades\\Auth',
      'Benchmark' => 'Illuminate\\Support\\Benchmark',
      'Blade' => 'Illuminate\\Support\\Facades\\Blade',
      'Broadcast' => 'Illuminate\\Support\\Facades\\Broadcast',
      'Bus' => 'Illuminate\\Support\\Facades\\Bus',
      'Cache' => 'Illuminate\\Support\\Facades\\Cache',
      'Concurrency' => 'Illuminate\\Support\\Facades\\Concurrency',
      'Config' => 'Illuminate\\Support\\Facades\\Config',
      'Context' => 'Illuminate\\Support\\Facades\\Context',
      'Cookie' => 'Illuminate\\Support\\Facades\\Cookie',
      'Crypt' => 'Illuminate\\Support\\Facades\\Crypt',
      'Date' => 'Illuminate\\Support\\Facades\\Date',
      'DB' => 'Illuminate\\Support\\Facades\\DB',
      'Eloquent' => 'Illuminate\\Database\\Eloquent\\Model',
      'Event' => 'Illuminate\\Support\\Facades\\Event',
      'File' => 'Illuminate\\Support\\Facades\\File',
      'Gate' => 'Illuminate\\Support\\Facades\\Gate',
      'Hash' => 'Illuminate\\Support\\Facades\\Hash',
      'Http' => 'Illuminate\\Support\\Facades\\Http',
      'Js' => 'Illuminate\\Support\\Js',
      'Lang' => 'Illuminate\\Support\\Facades\\Lang',
      'Log' => 'Illuminate\\Support\\Facades\\Log',
      'Mail' => 'Illuminate\\Support\\Facades\\Mail',
      'Notification' => 'Illuminate\\Support\\Facades\\Notification',
      'Number' => 'Illuminate\\Support\\Number',
      'Password' => 'Illuminate\\Support\\Facades\\Password',
      'Process' => 'Illuminate\\Support\\Facades\\Process',
      'Queue' => 'Illuminate\\Support\\Facades\\Queue',
      'RateLimiter' => 'Illuminate\\Support\\Facades\\RateLimiter',
      'Redirect' => 'Illuminate\\Support\\Facades\\Redirect',
      'Request' => 'Illuminate\\Support\\Facades\\Request',
      'Response' => 'Illuminate\\Support\\Facades\\Response',
      'Route' => 'Illuminate\\Support\\Facades\\Route',
      'Schedule' => 'Illuminate\\Support\\Facades\\Schedule',
      'Schema' => 'Illuminate\\Support\\Facades\\Schema',
      'Session' => 'Illuminate\\Support\\Facades\\Session',
      'Storage' => 'Illuminate\\Support\\Facades\\Storage',
      'Str' => 'Illuminate\\Support\\Str',
      'URL' => 'Illuminate\\Support\\Facades\\URL',
      'Uri' => 'Illuminate\\Support\\Uri',
      'Validator' => 'Illuminate\\Support\\Facades\\Validator',
      'View' => 'Illuminate\\Support\\Facades\\View',
      'Vite' => 'Illuminate\\Support\\Facades\\Vite',
    ),
  ),
  'auth' => 
  array (
    'defaults' => 
    array (
      'guard' => 'web',
      'passwords' => 'users',
    ),
    'guards' => 
    array (
      'web' => 
      array (
        'driver' => 'session',
        'provider' => 'users',
      ),
      'sanctum' => 
      array (
        'driver' => 'sanctum',
        'provider' => NULL,
      ),
    ),
    'providers' => 
    array (
      'users' => 
      array (
        'driver' => 'eloquent',
        'model' => 'App\\Models\\User',
      ),
    ),
    'passwords' => 
    array (
      'users' => 
      array (
        'provider' => 'users',
        'table' => 'password_reset_tokens',
        'expire' => 60,
        'throttle' => 60,
      ),
    ),
    'password_timeout' => 10800,
  ),
  'aws' => 
  array (
    'region' => 'us-east-1',
    'version' => 'latest',
    'ua_append' => 
    array (
      0 => 'L5MOD/3.7.0',
    ),
    'credentials' => false,
  ),
  'blade-icons' => 
  array (
    'sets' => 
    array (
      'default' => 
      array (
        'path' => 'resources/svg',
        'disk' => '',
        'prefix' => 'icon',
        'fallback' => '',
        'class' => '',
        'attributes' => 
        array (
        ),
      ),
    ),
    'class' => '',
    'attributes' => 
    array (
    ),
    'fallback' => '',
    'components' => 
    array (
      'disabled' => false,
      'default' => 'icon',
    ),
  ),
  'cache' => 
  array (
    'default' => 'file',
    'stores' => 
    array (
      'array' => 
      array (
        'driver' => 'array',
        'serialize' => false,
      ),
      'database' => 
      array (
        'driver' => 'database',
        'connection' => NULL,
        'table' => 'cache',
        'lock_connection' => NULL,
        'lock_table' => NULL,
      ),
      'file' => 
      array (
        'driver' => 'file',
        'path' => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\storage\\framework/cache/data',
        'lock_path' => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\storage\\framework/cache/data',
      ),
      'memcached' => 
      array (
        'driver' => 'memcached',
        'persistent_id' => NULL,
        'sasl' => 
        array (
          0 => NULL,
          1 => NULL,
        ),
        'options' => 
        array (
        ),
        'servers' => 
        array (
          0 => 
          array (
            'host' => '127.0.0.1',
            'port' => 11211,
            'weight' => 100,
          ),
        ),
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'cache',
        'lock_connection' => 'default',
      ),
      'dynamodb' => 
      array (
        'driver' => 'dynamodb',
        'key' => '',
        'secret' => '',
        'region' => 'us-east-1',
        'table' => 'cache',
        'endpoint' => NULL,
      ),
      'octane' => 
      array (
        'driver' => 'octane',
      ),
    ),
    'prefix' => 'arena-doviz-cache-',
  ),
  'chart-of-accounts' => 
  array (
    'default' => 
    array (
      'current_asset' => 
      array (
        'Cash and Cash Equivalents' => 
        array (
          'description' => 'The most liquid assets a company holds. This includes physical currency, bank balances, and short-term investments a company can quickly convert to cash.',
          'multi_currency' => true,
          'base_code' => '1000',
          'bank_account_type' => 'depository',
          'inverse_cash_flow' => false,
          'accounts' => 
          array (
            'Cash on Hand' => 
            array (
              'description' => 'The amount of money held by the company in the form of cash.',
            ),
          ),
        ),
        'Receivables' => 
        array (
          'description' => 'Amounts owed to the company for goods sold or services rendered, including accounts receivable, notes receivable, and other receivables.',
          'multi_currency' => false,
          'base_code' => '1100',
          'inverse_cash_flow' => true,
          'accounts' => 
          array (
            'Accounts Receivable' => 
            array (
              'description' => 'The amount of money owed to the company by customers who have not yet paid for goods or services received.',
            ),
          ),
        ),
        'Input Tax Recoverable' => 
        array (
          'description' => 'The amount of sales tax paid on purchases that can be recovered from the government.',
          'multi_currency' => false,
          'base_code' => '1150',
          'inverse_cash_flow' => true,
          'adjustment_category' => 'tax',
          'adjustment_type' => 'purchase',
          'adjustment_recoverable' => true,
          'accounts' => 
          array (
            'Input Tax' => 
            array (
              'description' => NULL,
            ),
          ),
        ),
        'Inventory' => 
        array (
          'description' => 'The raw materials, work-in-progress goods and completely finished goods that are considered to be the portion of a business\'s assets that are ready or will be ready for sale.',
          'multi_currency' => true,
          'base_code' => '1200',
          'inverse_cash_flow' => true,
        ),
        'Prepaid and Deferred Charges' => 
        array (
          'description' => 'Payments made in advance for future goods or services, such as insurance premiums, rent, and prepaid taxes.',
          'multi_currency' => true,
          'base_code' => '1300',
          'inverse_cash_flow' => true,
        ),
        'Other Current Assets' => 
        array (
          'description' => 'Other assets that are expected to be converted to cash, sold, or consumed within one year or the business\'s operating cycle.',
          'multi_currency' => true,
          'base_code' => '1400',
          'inverse_cash_flow' => true,
        ),
      ),
      'non_current_asset' => 
      array (
        'Long-Term Investments' => 
        array (
          'description' => 'Investments in securities like bonds and stocks, investments in other companies, or real estate held for more than one year, aiming for long-term benefits.',
          'multi_currency' => true,
          'base_code' => '1500',
          'inverse_cash_flow' => true,
        ),
        'Fixed Assets' => 
        array (
          'description' => 'Physical, tangible assets used in the business\'s operations with a useful life exceeding one year, such as buildings, machinery, and vehicles. These assets are subject to depreciation.',
          'multi_currency' => true,
          'base_code' => '1600',
          'inverse_cash_flow' => true,
        ),
        'Intangible Assets' => 
        array (
          'description' => 'Assets lacking physical substance but offering value to the business, like patents, copyrights, trademarks, software, and goodwill.',
          'multi_currency' => true,
          'base_code' => '1700',
          'inverse_cash_flow' => true,
        ),
        'Other Non-Current Assets' => 
        array (
          'description' => 'Includes long-term assets not classified in the above categories, such as long-term prepaid expenses, deferred tax assets, and loans made to other entities that are not expected to be settled within the next year.',
          'multi_currency' => true,
          'base_code' => '1800',
          'inverse_cash_flow' => true,
        ),
      ),
      'contra_asset' => 
      array (
        'Depreciation and Amortization' => 
        array (
          'description' => 'Accounts that accumulate depreciation of tangible assets and amortization of intangible assets, reflecting the reduction in value over time.',
          'multi_currency' => false,
          'base_code' => '1900',
          'inverse_cash_flow' => false,
          'accounts' => 
          array (
            'Accumulated Depreciation' => 
            array (
              'description' => 'Used to account for the depreciation of fixed assets over time, offsetting assets like equipment or property.',
            ),
          ),
        ),
        'Allowances for Receivables' => 
        array (
          'description' => 'Accounts representing estimated uncollected receivables, used to adjust the value of gross receivables to a realistic collectible amount.',
          'multi_currency' => false,
          'base_code' => '1940',
          'inverse_cash_flow' => false,
          'accounts' => 
          array (
            'Allowance for Doubtful Accounts' => 
            array (
              'description' => 'Used to account for potential bad debts that may not be collectable, offsetting receivables.',
            ),
          ),
        ),
        'Valuation Adjustments' => 
        array (
          'description' => 'Accounts used to record adjustments in asset values due to impairments, market changes, or other factors affecting their recoverable amount.',
          'multi_currency' => false,
          'base_code' => '1950',
          'inverse_cash_flow' => false,
        ),
      ),
      'current_liability' => 
      array (
        'Supplier Obligations' => 
        array (
          'description' => 'Liabilities arising from purchases of goods or services from suppliers, not yet paid for. This can include individual accounts payable and trade credits.',
          'multi_currency' => true,
          'base_code' => '2000',
          'inverse_cash_flow' => false,
          'accounts' => 
          array (
            'Accounts Payable' => 
            array (
              'description' => 'The amount of money owed by the company to suppliers for goods or services received.',
            ),
          ),
        ),
        'Accrued Expenses and Liabilities' => 
        array (
          'description' => 'Expenses that have been incurred but not yet paid, including wages, utilities, interest, and taxes. This category can house various accrued expense accounts.',
          'multi_currency' => false,
          'base_code' => '2100',
          'inverse_cash_flow' => false,
        ),
        'Sales Taxes' => 
        array (
          'description' => 'The amount of money owed to the government for sales tax collected from customers.',
          'multi_currency' => false,
          'base_code' => '2150',
          'inverse_cash_flow' => false,
          'adjustment_category' => 'tax',
          'adjustment_type' => 'sales',
          'adjustment_recoverable' => false,
          'accounts' => 
          array (
            'Sales Tax' => 
            array (
              'description' => NULL,
            ),
          ),
        ),
        'Short-Term Borrowings' => 
        array (
          'description' => 'Debt obligations due within the next year, such as bank loans, lines of credit, and short-term notes. This category can cover multiple short-term debt accounts.',
          'multi_currency' => true,
          'base_code' => '2200',
          'inverse_cash_flow' => false,
        ),
        'Customer Deposits and Advances' => 
        array (
          'description' => 'Funds received in advance for goods or services to be provided in the future, including customer deposits and prepayments.',
          'multi_currency' => true,
          'base_code' => '2300',
          'inverse_cash_flow' => false,
        ),
        'Other Current Liabilities' => 
        array (
          'description' => 'A grouping for miscellaneous short-term liabilities not covered in other categories, like the current portion of long-term debts, short-term provisions, and other similar obligations.',
          'multi_currency' => true,
          'base_code' => '2400',
          'inverse_cash_flow' => false,
        ),
      ),
      'non_current_liability' => 
      array (
        'Long-Term Borrowings' => 
        array (
          'description' => 'Obligations such as bonds, mortgages, and loans with a maturity of more than one year, covering various types of long-term debt instruments.',
          'multi_currency' => true,
          'base_code' => '2500',
          'inverse_cash_flow' => false,
        ),
        'Deferred Tax Liabilities' => 
        array (
          'description' => 'Taxes incurred in the current period but payable in a future period, typically due to differences in accounting methods between tax reporting and financial reporting.',
          'multi_currency' => false,
          'base_code' => '2600',
          'inverse_cash_flow' => false,
        ),
        'Other Long-Term Liabilities' => 
        array (
          'description' => 'Liabilities not due within the next year and not classified as long-term debt or deferred taxes, including pension liabilities, lease obligations, and long-term provisions.',
          'multi_currency' => true,
          'base_code' => '2700',
          'inverse_cash_flow' => false,
        ),
      ),
      'contra_liability' => 
      array (
        'Accumulated Amortization of Debt Discount' => 
        array (
          'description' => 'Accumulated amount representing the reduction of bond or loan liabilities, reflecting the difference between the face value and the discounted issuance price over time.',
          'multi_currency' => false,
          'base_code' => '2900',
          'inverse_cash_flow' => false,
        ),
        'Valuation Adjustments for Liabilities' => 
        array (
          'description' => 'Adjustments made to the recorded value of liabilities, such as changes in fair value of derivative liabilities or adjustments for hedging activities.',
          'multi_currency' => false,
          'base_code' => '2950',
          'inverse_cash_flow' => false,
        ),
      ),
      'equity' => 
      array (
        'Contributed Capital' => 
        array (
          'description' => 'Funds provided by owners or shareholders for starting the business and subsequent capital injections. Reflects the financial commitment of the owner(s) or shareholders to the business.',
          'multi_currency' => true,
          'base_code' => '3000',
          'inverse_cash_flow' => false,
          'accounts' => 
          array (
            'Owner\'s Investment' => 
            array (
              'description' => 'The amount of money invested by the owner(s) or shareholders to start or expand the business.',
            ),
          ),
        ),
      ),
      'contra_equity' => 
      array (
        'Contra Equity' => 
        array (
          'description' => 'Equity that is deducted from gross equity to arrive at net equity. This includes treasury stock, which is stock that has been repurchased by the company.',
          'multi_currency' => false,
          'base_code' => '3900',
          'inverse_cash_flow' => true,
          'accounts' => 
          array (
            'Owner\'s Drawings' => 
            array (
              'description' => 'The amount of money withdrawn by the owner(s) or shareholders from the business for personal use, reducing equity.',
            ),
          ),
        ),
      ),
      'operating_revenue' => 
      array (
        'Product Sales' => 
        array (
          'description' => 'Income from selling physical or digital products. Includes revenue from all product lines or categories.',
          'multi_currency' => false,
          'base_code' => '4000',
          'inverse_cash_flow' => false,
          'accounts' => 
          array (
            'Product Sales' => 
            array (
              'description' => 'The amount of money earned from selling physical or digital products.',
            ),
          ),
        ),
        'Service Revenue' => 
        array (
          'description' => 'Income earned from providing services, encompassing activities like consulting, maintenance, and repair services.',
          'multi_currency' => false,
          'base_code' => '4100',
          'inverse_cash_flow' => false,
        ),
        'Other Operating Revenue' => 
        array (
          'description' => 'Income from other business operations not classified as product sales or services, such as rental income, royalties, or income from licensing agreements.',
          'multi_currency' => false,
          'base_code' => '4200',
          'inverse_cash_flow' => false,
        ),
      ),
      'non_operating_revenue' => 
      array (
        'Investment Income' => 
        array (
          'description' => 'Earnings from investments, including dividends, interest from securities, and profits from real estate investments.',
          'multi_currency' => false,
          'base_code' => '4500',
          'inverse_cash_flow' => false,
          'accounts' => 
          array (
            'Dividends' => 
            array (
              'description' => 'The amount of money received from investments in shares of other companies.',
            ),
            'Interest Earned' => 
            array (
              'description' => 'The amount of money earned from interest-bearing investments like bonds, certificates of deposit, or savings accounts.',
            ),
          ),
        ),
        'Gains from Asset Disposition' => 
        array (
          'description' => 'Profits from selling assets like property, equipment, or investments, excluding regular sales of inventory.',
          'multi_currency' => false,
          'base_code' => '4600',
          'inverse_cash_flow' => false,
        ),
        'Other Non-Operating Revenue' => 
        array (
          'description' => 'Income from sources not related to the main business activities, such as legal settlements, insurance recoveries, or gains from foreign exchange transactions.',
          'multi_currency' => false,
          'base_code' => '4700',
          'inverse_cash_flow' => false,
          'accounts' => 
          array (
            'Gain on Foreign Exchange' => 
            array (
              'description' => 'The amount of money earned from foreign exchange transactions due to favorable exchange rate changes.',
            ),
          ),
        ),
      ),
      'contra_revenue' => 
      array (
        'Contra Revenue' => 
        array (
          'description' => 'Revenue that is deducted from gross revenue to arrive at net revenue. This includes sales discounts, returns, and allowances.',
          'multi_currency' => false,
          'base_code' => '4900',
          'inverse_cash_flow' => false,
          'accounts' => 
          array (
            'Sales Returns and Allowances' => 
            array (
              'description' => 'The amount of money returned to customers or deducted from sales due to returned goods or allowances granted.',
            ),
          ),
        ),
        'Sales Discounts' => 
        array (
          'description' => 'The amount of money deducted from sales due to discounts offered to customers for early payment or other reasons.',
          'multi_currency' => false,
          'base_code' => '4925',
          'inverse_cash_flow' => false,
          'adjustment_category' => 'discount',
          'adjustment_type' => 'sales',
          'adjustment_recoverable' => false,
          'accounts' => 
          array (
            'Sales Discount' => 
            array (
              'description' => NULL,
            ),
          ),
        ),
      ),
      'uncategorized_revenue' => 
      array (
        'Uncategorized Revenue' => 
        array (
          'description' => 'Revenue that has not been categorized into other revenue categories.',
          'multi_currency' => false,
          'base_code' => '4950',
          'inverse_cash_flow' => false,
          'accounts' => 
          array (
            'Uncategorized Income' => 
            array (
              'description' => 'Revenue from other business operations that don\'t fall under regular sales or services. This account is used as the default for all new transactions.',
            ),
          ),
        ),
      ),
      'operating_expense' => 
      array (
        'Cost of Goods Sold' => 
        array (
          'description' => 'Direct costs attributable to the production of goods sold by a company. This includes material costs and direct labor.',
          'multi_currency' => false,
          'base_code' => '5000',
          'inverse_cash_flow' => true,
        ),
        'Payroll and Employee Benefits' => 
        array (
          'description' => 'Expenses related to employee compensation, including salaries, wages, bonuses, commissions, and payroll taxes.',
          'multi_currency' => false,
          'base_code' => '5050',
          'inverse_cash_flow' => true,
          'accounts' => 
          array (
            'Salaries and Wages' => 
            array (
              'description' => 'The amount of money paid to employees for their work, including regular salaries and hourly wages.',
            ),
            'Payroll Employer Taxes and Contributions' => 
            array (
              'description' => 'The amount of money paid by the employer for payroll taxes and contributions, such as social security, unemployment, and workers\' compensation.',
            ),
            'Employee Benefits' => 
            array (
              'description' => 'The amount of money spent on employee benefits, such as health insurance, retirement plans, and other benefits.',
            ),
            'Payroll Processing Fees' => 
            array (
              'description' => 'The amount of money paid to third-party payroll processors for payroll services.',
            ),
          ),
        ),
        'Facility Expenses' => 
        array (
          'description' => 'Costs incurred for business premises, including rent or lease payments, property taxes, utilities, and building maintenance.',
          'multi_currency' => false,
          'base_code' => '5100',
          'inverse_cash_flow' => true,
          'accounts' => 
          array (
            'Rent or Lease Payments' => 
            array (
              'description' => 'The amount of money paid for renting or leasing business premises.',
            ),
            'Property Taxes' => 
            array (
              'description' => 'The amount of money paid for taxes on business property.',
            ),
            'Building Maintenance' => 
            array (
              'description' => 'The amount of money spent on maintaining business premises, including repairs and cleaning.',
            ),
            'Utilities' => 
            array (
              'description' => 'The amount of money paid for business utilities, such as electricity, water, and gas.',
            ),
            'Property Insurance' => 
            array (
              'description' => 'The amount of money paid for insurance on business property.',
            ),
          ),
        ),
        'General and Administrative' => 
        array (
          'description' => 'Expenses related to general business operations, such as office supplies, insurance, and professional fees.',
          'multi_currency' => false,
          'base_code' => '5150',
          'inverse_cash_flow' => true,
          'accounts' => 
          array (
            'Food and Drink' => 
            array (
              'description' => 'The amount of money spent on food and drink for business purposes, such as office snacks, meals, and catering.',
            ),
            'Transportation' => 
            array (
              'description' => 'The amount of money spent on business transportation, such as fuel, vehicle maintenance, and public transportation.',
            ),
            'Travel' => 
            array (
              'description' => 'The amount of money spent on business travel, such as airfare, hotels, and rental cars.',
            ),
            'Entertainment' => 
            array (
              'description' => 'The amount of money spent on business entertainment, such as client dinners, events, and tickets.',
            ),
            'Office Supplies' => 
            array (
              'description' => 'The amount of money spent on office supplies, such as paper, ink, and stationery.',
            ),
            'Office Equipment and Furniture' => 
            array (
              'description' => 'The amount of money spent on office equipment and furniture, such as computers, printers, and desks.',
            ),
            'Legal and Professional Fees' => 
            array (
              'description' => 'The amount of money paid for legal and professional services, such as legal advice, accounting, and consulting.',
            ),
            'Software and Subscriptions' => 
            array (
              'description' => 'The amount of money spent on software and subscriptions, such as SaaS products, cloud services, and digital tools.',
            ),
          ),
        ),
        'Marketing and Advertising' => 
        array (
          'description' => 'Expenses related to marketing and advertising activities, such as advertising campaigns, promotional events, and marketing materials.',
          'multi_currency' => false,
          'base_code' => '5200',
          'inverse_cash_flow' => true,
          'accounts' => 
          array (
            'Advertising' => 
            array (
              'description' => 'The amount of money spent on advertising campaigns, including print, digital, and outdoor advertising.',
            ),
            'Marketing' => 
            array (
              'description' => 'The amount of money spent on marketing activities, such as content creation, social media, and email marketing.',
            ),
          ),
        ),
        'Research and Development' => 
        array (
          'description' => 'Expenses incurred in the process of researching and developing new products or services.',
          'multi_currency' => false,
          'base_code' => '5250',
          'inverse_cash_flow' => true,
        ),
        'Other Operating Expenses' => 
        array (
          'description' => 'Miscellaneous expenses not categorized elsewhere, such as research and development costs, legal fees, and other irregular expenses.',
          'multi_currency' => false,
          'base_code' => '5300',
          'inverse_cash_flow' => true,
        ),
      ),
      'non_operating_expense' => 
      array (
        'Interest and Financing Costs' => 
        array (
          'description' => 'Expenses related to borrowing and financing, such as interest payments on loans, bonds, and credit lines.',
          'multi_currency' => false,
          'base_code' => '5500',
          'inverse_cash_flow' => true,
        ),
        'Tax Expenses' => 
        array (
          'description' => 'Various taxes incurred by the business, including income tax, sales tax, property tax, and payroll tax.',
          'multi_currency' => false,
          'base_code' => '5600',
          'inverse_cash_flow' => true,
        ),
        'Other Non-Operating Expense' => 
        array (
          'description' => 'Expenses not related to primary business activities, like losses from asset disposals, legal settlements, restructuring costs, or foreign exchange losses.',
          'multi_currency' => false,
          'base_code' => '5700',
          'inverse_cash_flow' => true,
          'accounts' => 
          array (
            'Loss on Foreign Exchange' => 
            array (
              'description' => 'The amount of money lost from foreign exchange transactions due to unfavorable exchange rate changes.',
            ),
          ),
        ),
      ),
      'contra_expense' => 
      array (
        'Contra Expenses' => 
        array (
          'description' => 'Expenses that are deducted from gross expenses to arrive at net expenses. This includes purchase discounts, returns, and allowances.',
          'multi_currency' => false,
          'base_code' => '5900',
          'inverse_cash_flow' => true,
          'accounts' => 
          array (
            'Purchase Returns and Allowances' => 
            array (
              'description' => 'The amount of money returned to suppliers or deducted from purchases due to returned goods or allowances granted.',
            ),
          ),
        ),
        'Purchase Discounts' => 
        array (
          'description' => 'The amount of money deducted from purchases due to discounts offered by suppliers for early payment or other reasons.',
          'multi_currency' => false,
          'base_code' => '5925',
          'inverse_cash_flow' => true,
          'adjustment_category' => 'discount',
          'adjustment_type' => 'purchase',
          'adjustment_recoverable' => false,
          'accounts' => 
          array (
            'Purchase Discount' => 
            array (
              'description' => NULL,
            ),
          ),
        ),
      ),
      'uncategorized_expense' => 
      array (
        'Uncategorized Expense' => 
        array (
          'description' => 'Expenses that have not been categorized into other expense categories.',
          'multi_currency' => false,
          'base_code' => '5950',
          'inverse_cash_flow' => true,
          'accounts' => 
          array (
            'Uncategorized Expense' => 
            array (
              'description' => 'Expenses not classified into regular expense categories. This account is used as the default for all new transactions.',
            ),
          ),
        ),
      ),
    ),
  ),
  'database' => 
  array (
    'default' => 'mysql',
    'connections' => 
    array (
      'sqlite' => 
      array (
        'driver' => 'sqlite',
        'url' => NULL,
        'database' => 'arena_doviz',
        'prefix' => '',
        'foreign_key_constraints' => true,
        'busy_timeout' => NULL,
        'journal_mode' => NULL,
        'synchronous' => NULL,
      ),
      'mysql' => 
      array (
        'driver' => 'mysql',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'arena_doviz',
        'username' => 'root',
        'password' => '',
        'unix_socket' => '',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'prefix_indexes' => true,
        'strict' => true,
        'engine' => NULL,
        'options' => 
        array (
        ),
      ),
      'mariadb' => 
      array (
        'driver' => 'mariadb',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'arena_doviz',
        'username' => 'root',
        'password' => '',
        'unix_socket' => '',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'prefix_indexes' => true,
        'strict' => true,
        'engine' => NULL,
        'options' => 
        array (
        ),
      ),
      'pgsql' => 
      array (
        'driver' => 'pgsql',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'arena_doviz',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8',
        'prefix' => '',
        'prefix_indexes' => true,
        'search_path' => 'public',
        'sslmode' => 'prefer',
      ),
      'sqlsrv' => 
      array (
        'driver' => 'sqlsrv',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'arena_doviz',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8',
        'prefix' => '',
        'prefix_indexes' => true,
      ),
    ),
    'migrations' => 
    array (
      'table' => 'migrations',
      'update_date_on_publish' => true,
    ),
    'redis' => 
    array (
      'client' => 'phpredis',
      'options' => 
      array (
        'cluster' => 'redis',
        'prefix' => 'arena-doviz-database-',
        'persistent' => false,
      ),
      'default' => 
      array (
        'url' => NULL,
        'host' => 'redis',
        'username' => NULL,
        'password' => NULL,
        'port' => '6379',
        'database' => '0',
      ),
      'cache' => 
      array (
        'url' => NULL,
        'host' => 'redis',
        'username' => NULL,
        'password' => NULL,
        'port' => '6379',
        'database' => '1',
      ),
    ),
  ),
  'debugbar' => 
  array (
    'enabled' => false,
    'hide_empty_tabs' => true,
    'except' => 
    array (
      0 => 'telescope*',
      1 => 'horizon*',
    ),
    'storage' => 
    array (
      'enabled' => true,
      'open' => NULL,
      'driver' => 'file',
      'path' => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\storage\\debugbar',
      'connection' => NULL,
      'provider' => '',
      'hostname' => '127.0.0.1',
      'port' => 2304,
    ),
    'editor' => 'phpstorm',
    'remote_sites_path' => NULL,
    'local_sites_path' => NULL,
    'include_vendors' => true,
    'capture_ajax' => true,
    'add_ajax_timing' => false,
    'ajax_handler_auto_show' => false,
    'ajax_handler_enable_tab' => true,
    'defer_datasets' => false,
    'error_handler' => false,
    'clockwork' => false,
    'collectors' => 
    array (
      'phpinfo' => false,
      'messages' => true,
      'time' => true,
      'memory' => true,
      'exceptions' => true,
      'log' => true,
      'db' => true,
      'views' => true,
      'route' => false,
      'auth' => false,
      'gate' => true,
      'session' => false,
      'symfony_request' => true,
      'mail' => true,
      'laravel' => true,
      'events' => false,
      'default_request' => false,
      'logs' => false,
      'files' => false,
      'config' => false,
      'cache' => false,
      'models' => true,
      'livewire' => true,
      'jobs' => false,
      'pennant' => false,
    ),
    'options' => 
    array (
      'time' => 
      array (
        'memory_usage' => false,
      ),
      'messages' => 
      array (
        'trace' => true,
      ),
      'memory' => 
      array (
        'reset_peak' => false,
        'with_baseline' => false,
        'precision' => 0,
      ),
      'auth' => 
      array (
        'show_name' => true,
        'show_guards' => true,
      ),
      'db' => 
      array (
        'with_params' => true,
        'exclude_paths' => 
        array (
        ),
        'backtrace' => true,
        'backtrace_exclude_paths' => 
        array (
        ),
        'timeline' => false,
        'duration_background' => true,
        'explain' => 
        array (
          'enabled' => false,
        ),
        'hints' => false,
        'show_copy' => true,
        'slow_threshold' => false,
        'memory_usage' => false,
        'soft_limit' => 100,
        'hard_limit' => 500,
      ),
      'mail' => 
      array (
        'timeline' => true,
        'show_body' => true,
      ),
      'views' => 
      array (
        'timeline' => true,
        'data' => false,
        'group' => 50,
        'exclude_paths' => 
        array (
          0 => 'vendor/filament',
        ),
      ),
      'route' => 
      array (
        'label' => true,
      ),
      'session' => 
      array (
        'hiddens' => 
        array (
        ),
      ),
      'symfony_request' => 
      array (
        'label' => true,
        'hiddens' => 
        array (
        ),
      ),
      'events' => 
      array (
        'data' => false,
      ),
      'logs' => 
      array (
        'file' => NULL,
      ),
      'cache' => 
      array (
        'values' => true,
      ),
    ),
    'inject' => true,
    'route_prefix' => '_debugbar',
    'route_middleware' => 
    array (
    ),
    'route_domain' => NULL,
    'theme' => 'auto',
    'debug_backtrace_limit' => 50,
  ),
  'filament' => 
  array (
    'broadcasting' => 
    array (
    ),
    'default_filesystem_disk' => 'public',
    'assets_path' => NULL,
    'cache_path' => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\bootstrap/cache/filament',
    'livewire_loading_delay' => 'default',
    'system_route_prefix' => 'filament',
  ),
  'filesystems' => 
  array (
    'default' => 'local',
    'disks' => 
    array (
      'local' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\storage\\app/private',
        'serve' => true,
        'throw' => false,
        'report' => false,
      ),
      'public' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\storage\\app/public',
        'url' => 'http://localhost:8000/storage',
        'visibility' => 'public',
        'throw' => false,
        'report' => false,
      ),
      's3' => 
      array (
        'driver' => 's3',
        'key' => '',
        'secret' => '',
        'region' => 'us-east-1',
        'bucket' => '',
        'url' => NULL,
        'endpoint' => NULL,
        'use_path_style_endpoint' => false,
        'throw' => false,
        'report' => false,
      ),
    ),
    'links' => 
    array (
      'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\public\\storage' => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\storage\\app/public',
    ),
  ),
  'livewire' => 
  array (
    'class_namespace' => 'App\\Livewire',
    'view_path' => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\resources\\views/livewire',
    'layout' => 'components.layouts.app',
    'lazy_placeholder' => NULL,
    'temporary_file_upload' => 
    array (
      'disk' => NULL,
      'rules' => NULL,
      'directory' => NULL,
      'middleware' => NULL,
      'preview_mimes' => 
      array (
        0 => 'png',
        1 => 'gif',
        2 => 'bmp',
        3 => 'svg',
        4 => 'wav',
        5 => 'mp4',
        6 => 'mov',
        7 => 'avi',
        8 => 'wmv',
        9 => 'mp3',
        10 => 'm4a',
        11 => 'jpg',
        12 => 'jpeg',
        13 => 'mpga',
        14 => 'webp',
        15 => 'wma',
      ),
      'max_upload_time' => 5,
    ),
    'render_on_redirect' => false,
    'legacy_model_binding' => false,
    'inject_assets' => true,
    'navigate' => 
    array (
      'show_progress_bar' => true,
      'progress_bar_color' => '#2299dd',
    ),
    'inject_morph_markers' => true,
    'pagination_theme' => 'tailwind',
  ),
  'logging' => 
  array (
    'default' => 'stack',
    'deprecations' => 
    array (
      'channel' => NULL,
      'trace' => false,
    ),
    'channels' => 
    array (
      'stack' => 
      array (
        'driver' => 'stack',
        'channels' => 
        array (
          0 => 'single',
        ),
        'ignore_exceptions' => false,
      ),
      'single' => 
      array (
        'driver' => 'single',
        'path' => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\storage\\logs/laravel.log',
        'level' => 'debug',
        'replace_placeholders' => true,
      ),
      'daily' => 
      array (
        'driver' => 'daily',
        'path' => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\storage\\logs/laravel.log',
        'level' => 'debug',
        'days' => 14,
        'replace_placeholders' => true,
      ),
      'slack' => 
      array (
        'driver' => 'slack',
        'url' => NULL,
        'username' => 'Laravel Log',
        'emoji' => ':boom:',
        'level' => 'debug',
        'replace_placeholders' => true,
      ),
      'papertrail' => 
      array (
        'driver' => 'monolog',
        'level' => 'debug',
        'handler' => 'Monolog\\Handler\\SyslogUdpHandler',
        'handler_with' => 
        array (
          'host' => NULL,
          'port' => NULL,
          'connectionString' => 'tls://:',
        ),
        'processors' => 
        array (
          0 => 'Monolog\\Processor\\PsrLogMessageProcessor',
        ),
      ),
      'stderr' => 
      array (
        'driver' => 'monolog',
        'level' => 'debug',
        'handler' => 'Monolog\\Handler\\StreamHandler',
        'handler_with' => 
        array (
          'stream' => 'php://stderr',
        ),
        'formatter' => NULL,
        'processors' => 
        array (
          0 => 'Monolog\\Processor\\PsrLogMessageProcessor',
        ),
      ),
      'syslog' => 
      array (
        'driver' => 'syslog',
        'level' => 'debug',
        'facility' => 8,
        'replace_placeholders' => true,
      ),
      'errorlog' => 
      array (
        'driver' => 'errorlog',
        'level' => 'debug',
        'replace_placeholders' => true,
      ),
      'null' => 
      array (
        'driver' => 'monolog',
        'handler' => 'Monolog\\Handler\\NullHandler',
      ),
      'emergency' => 
      array (
        'path' => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\storage\\logs/laravel.log',
      ),
    ),
  ),
  'mail' => 
  array (
    'default' => 'smtp',
    'mailers' => 
    array (
      'smtp' => 
      array (
        'transport' => 'smtp',
        'scheme' => NULL,
        'url' => NULL,
        'host' => 'mailhog',
        'port' => '1025',
        'username' => NULL,
        'password' => NULL,
        'timeout' => NULL,
        'local_domain' => 'localhost',
      ),
      'ses' => 
      array (
        'transport' => 'ses',
      ),
      'postmark' => 
      array (
        'transport' => 'postmark',
      ),
      'resend' => 
      array (
        'transport' => 'resend',
      ),
      'sendmail' => 
      array (
        'transport' => 'sendmail',
        'path' => '/usr/sbin/sendmail -bs -i',
      ),
      'log' => 
      array (
        'transport' => 'log',
        'channel' => NULL,
      ),
      'array' => 
      array (
        'transport' => 'array',
      ),
      'failover' => 
      array (
        'transport' => 'failover',
        'mailers' => 
        array (
          0 => 'smtp',
          1 => 'log',
        ),
        'retry_after' => 60,
      ),
      'roundrobin' => 
      array (
        'transport' => 'roundrobin',
        'mailers' => 
        array (
          0 => 'ses',
          1 => 'postmark',
        ),
        'retry_after' => 60,
      ),
    ),
    'from' => 
    array (
      'address' => '<EMAIL>',
      'name' => 'Arena Doviz',
    ),
    'markdown' => 
    array (
      'theme' => 'default',
      'paths' => 
      array (
        0 => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\resources\\views/vendor/mail',
      ),
    ),
  ),
  'money' => 
  array (
    'defaults' => 
    array (
      'currency' => 'USD',
      'convert' => false,
    ),
    'currencies' => 
    array (
      'AFN' => 
      array (
        'name' => 'Afghani',
        'code' => '971',
        'entity' => 'Afghanistan',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '؋',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'EUR' => 
      array (
        'name' => 'Euro',
        'code' => '978',
        'entity' => 'Åland Islands',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '€',
        'symbol_first' => true,
        'decimal_mark' => ',',
        'thousands_separator' => '.',
      ),
      'ALL' => 
      array (
        'name' => 'Lek',
        'code' => '008',
        'entity' => 'Albania',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'L',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'DZD' => 
      array (
        'name' => 'Algerian Dinar',
        'code' => '012',
        'entity' => 'Algeria',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'د.ج',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'USD' => 
      array (
        'name' => 'US Dollar',
        'code' => '840',
        'entity' => 'American Samoa',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'AOA' => 
      array (
        'name' => 'Kwanza',
        'code' => '973',
        'entity' => 'Angola',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'Kz',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'XCD' => 
      array (
        'name' => 'East Caribbean Dollar',
        'code' => '951',
        'entity' => 'Anguilla',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'ARS' => 
      array (
        'name' => 'Argentine Peso',
        'code' => '032',
        'entity' => 'Argentina',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => true,
        'decimal_mark' => ',',
        'thousands_separator' => '.',
      ),
      'AMD' => 
      array (
        'name' => 'Armenian Dram',
        'code' => '051',
        'entity' => 'Armenia',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '֏',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'AWG' => 
      array (
        'name' => 'Aruban Florin',
        'code' => '533',
        'entity' => 'Aruba',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'ƒ',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'AUD' => 
      array (
        'name' => 'Australian Dollar',
        'code' => '036',
        'entity' => 'Australia',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'AZN' => 
      array (
        'name' => 'Azerbaijan Manat',
        'code' => '944',
        'entity' => 'Azerbaijan',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '₼',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'BSD' => 
      array (
        'name' => 'Bahamian Dollar',
        'code' => '044',
        'entity' => 'Bahamas',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'BHD' => 
      array (
        'name' => 'Bahraini Dinar',
        'code' => '048',
        'entity' => 'Bahrain',
        'precision' => 3,
        'subunit' => 1000,
        'symbol' => '.د.ب',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'BDT' => 
      array (
        'name' => 'Taka',
        'code' => '050',
        'entity' => 'Bangladesh',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '৳',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'BBD' => 
      array (
        'name' => 'Barbados Dollar',
        'code' => '052',
        'entity' => 'Barbados',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'BYN' => 
      array (
        'name' => 'Belarusian Ruble',
        'code' => '933',
        'entity' => 'Belarus',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'Br',
        'symbol_first' => false,
        'decimal_mark' => ',',
        'thousands_separator' => '',
      ),
      'BZD' => 
      array (
        'name' => 'Belize Dollar',
        'code' => '084',
        'entity' => 'Belize',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'BZ$',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'XOF' => 
      array (
        'name' => 'CFA Franc BCEAO',
        'code' => '952',
        'entity' => 'Benin',
        'precision' => 0,
        'subunit' => 1,
        'symbol' => 'CFA',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'BMD' => 
      array (
        'name' => 'Bermudian Dollar',
        'code' => '060',
        'entity' => 'Bermuda',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'INR' => 
      array (
        'name' => 'Indian Rupee',
        'code' => '356',
        'entity' => 'India',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '₹',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'BTN' => 
      array (
        'name' => 'Ngultrum',
        'code' => '064',
        'entity' => 'Bhutan',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'Nu.',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'BOB' => 
      array (
        'name' => 'Boliviano',
        'code' => '068',
        'entity' => 'Bolivia',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$b',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'BOV' => 
      array (
        'name' => 'Mvdol',
        'code' => '984',
        'entity' => 'Bolivia',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'BOV',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'BAM' => 
      array (
        'name' => 'Convertible Mark',
        'code' => '977',
        'entity' => 'Bosnia And Herzegovina',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'KM',
        'symbol_first' => true,
        'decimal_mark' => ',',
        'thousands_separator' => '.',
      ),
      'BWP' => 
      array (
        'name' => 'Pula',
        'code' => '072',
        'entity' => 'Botswana',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'P',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'NOK' => 
      array (
        'name' => 'Norwegian Krone',
        'code' => '578',
        'entity' => 'Norway',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'kr',
        'symbol_first' => false,
        'decimal_mark' => ',',
        'thousands_separator' => '',
      ),
      'BRL' => 
      array (
        'name' => 'Brazilian Real',
        'code' => '986',
        'entity' => 'Brazil',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'R$',
        'symbol_first' => true,
        'decimal_mark' => ',',
        'thousands_separator' => '.',
      ),
      'BND' => 
      array (
        'name' => 'Brunei Dollar',
        'code' => '096',
        'entity' => 'Brunei Darussalam',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'BGN' => 
      array (
        'name' => 'Bulgarian Lev',
        'code' => '975',
        'entity' => 'Bulgaria',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'лв',
        'symbol_first' => false,
        'decimal_mark' => ',',
        'thousands_separator' => '',
      ),
      'BIF' => 
      array (
        'name' => 'Burundi Franc',
        'code' => '108',
        'entity' => 'Burundi',
        'precision' => 0,
        'subunit' => 1,
        'symbol' => 'FBu',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'CVE' => 
      array (
        'name' => 'Cabo Verde Escudo',
        'code' => '132',
        'entity' => 'Cabo Verde',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'KHR' => 
      array (
        'name' => 'Riel',
        'code' => '116',
        'entity' => 'Cambodia',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '៛',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'XAF' => 
      array (
        'name' => 'CFA Franc BEAC',
        'code' => '950',
        'entity' => 'Cameroon',
        'precision' => 0,
        'subunit' => 1,
        'symbol' => 'FCFA',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'CAD' => 
      array (
        'name' => 'Canadian Dollar',
        'code' => '124',
        'entity' => 'Canada',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'KYD' => 
      array (
        'name' => 'Cayman Islands Dollar',
        'code' => '136',
        'entity' => 'Cayman Islands',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'CLP' => 
      array (
        'name' => 'Chilean Peso',
        'code' => '152',
        'entity' => 'Chile',
        'precision' => 0,
        'subunit' => 1,
        'symbol' => '$',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => '.',
      ),
      'CNY' => 
      array (
        'name' => 'Yuan Renminbi',
        'code' => '156',
        'entity' => 'China',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '¥',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'COP' => 
      array (
        'name' => 'Colombian Peso',
        'code' => '170',
        'entity' => 'Colombia',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => true,
        'decimal_mark' => ',',
        'thousands_separator' => '.',
      ),
      'KMF' => 
      array (
        'name' => 'Comorian Franc ',
        'code' => '174',
        'entity' => 'Comoros',
        'precision' => 0,
        'subunit' => 1,
        'symbol' => 'CF',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'CDF' => 
      array (
        'name' => 'Congolese Franc',
        'code' => '976',
        'entity' => 'Congo',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'FC',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'NZD' => 
      array (
        'name' => 'New Zealand Dollar',
        'code' => '554',
        'entity' => 'New Zealand',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'CRC' => 
      array (
        'name' => 'Costa Rican Colon',
        'code' => '188',
        'entity' => 'Costa Rica',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '₡',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'CUP' => 
      array (
        'name' => 'Cuban Peso',
        'code' => '192',
        'entity' => 'Cuba',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '₱',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'CUC' => 
      array (
        'name' => 'Peso Convertible',
        'code' => '931',
        'entity' => 'Cuba',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'ANG' => 
      array (
        'name' => 'Netherlands Antillean Guilder',
        'code' => '532',
        'entity' => 'Curaçao',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'ƒ',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'CZK' => 
      array (
        'name' => 'Czech Koruna',
        'code' => '203',
        'entity' => 'Czech Republic',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'Kč',
        'symbol_first' => false,
        'decimal_mark' => ',',
        'thousands_separator' => '.',
      ),
      'DKK' => 
      array (
        'name' => 'Danish Krone',
        'code' => '208',
        'entity' => 'Denmark',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'kr',
        'symbol_first' => false,
        'decimal_mark' => ',',
        'thousands_separator' => '.',
      ),
      'DJF' => 
      array (
        'name' => 'Djibouti Franc',
        'code' => '262',
        'entity' => 'Djibouti',
        'precision' => 0,
        'subunit' => 1,
        'symbol' => 'Fdj',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'DOP' => 
      array (
        'name' => 'Dominican Peso',
        'code' => '214',
        'entity' => 'Dominican Republic',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'RD$',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'EGP' => 
      array (
        'name' => 'Egyptian Pound',
        'code' => '818',
        'entity' => 'Egypt',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '£',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'SVC' => 
      array (
        'name' => 'El Salvador Colon',
        'code' => '222',
        'entity' => 'El Salvador',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'ERN' => 
      array (
        'name' => 'Nakfa',
        'code' => '232',
        'entity' => 'Eritrea',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'Nfk',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'SZL' => 
      array (
        'name' => 'Lilangeni',
        'code' => '748',
        'entity' => 'Eswatini',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'E',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'ETB' => 
      array (
        'name' => 'Ethiopian Birr',
        'code' => '230',
        'entity' => 'Ethiopia',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'Br',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'FKP' => 
      array (
        'name' => 'Falkland Islands Pound',
        'code' => '238',
        'entity' => 'Falkland Islands (Malvinas)',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '£',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'FJD' => 
      array (
        'name' => 'Fiji Dollar',
        'code' => '242',
        'entity' => 'Fiji',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'XPF' => 
      array (
        'name' => 'CFP Franc',
        'code' => '953',
        'entity' => 'New Caledonia',
        'precision' => 0,
        'subunit' => 1,
        'symbol' => '₣',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'GMD' => 
      array (
        'name' => 'Dalasi',
        'code' => '270',
        'entity' => 'Gambia',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'D',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'GEL' => 
      array (
        'name' => 'Lari',
        'code' => '981',
        'entity' => 'Georgia',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '₾',
        'symbol_first' => false,
        'decimal_mark' => ',',
        'thousands_separator' => '.',
      ),
      'GHS' => 
      array (
        'name' => 'Ghana Cedi',
        'code' => '936',
        'entity' => 'Ghana',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'GH₵',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'GIP' => 
      array (
        'name' => 'Gibraltar Pound',
        'code' => '292',
        'entity' => 'Gibraltar',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '£',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'GTQ' => 
      array (
        'name' => 'Quetzal',
        'code' => '320',
        'entity' => 'Guatemala',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'Q',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'GBP' => 
      array (
        'name' => 'Pound Sterling',
        'code' => '826',
        'entity' => 'United Kingdom',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '£',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'GNF' => 
      array (
        'name' => 'Guinean Franc',
        'code' => '324',
        'entity' => 'Guinea',
        'precision' => 0,
        'subunit' => 1,
        'symbol' => 'FG',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'GYD' => 
      array (
        'name' => 'Guyana Dollar',
        'code' => '328',
        'entity' => 'Guyana',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'HTG' => 
      array (
        'name' => 'Gourde',
        'code' => '332',
        'entity' => 'Haiti',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'G',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'HNL' => 
      array (
        'name' => 'Lempira',
        'code' => '340',
        'entity' => 'Honduras',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'L',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'HKD' => 
      array (
        'name' => 'Hong Kong Dollar',
        'code' => '344',
        'entity' => 'Hong Kong',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'HUF' => 
      array (
        'name' => 'Forint',
        'code' => '348',
        'entity' => 'Hungary',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'Ft',
        'symbol_first' => false,
        'decimal_mark' => ',',
        'thousands_separator' => '',
      ),
      'ISK' => 
      array (
        'name' => 'Iceland Krona',
        'code' => '352',
        'entity' => 'Iceland',
        'precision' => 0,
        'subunit' => 1,
        'symbol' => 'kr',
        'symbol_first' => true,
        'decimal_mark' => ',',
        'thousands_separator' => '.',
      ),
      'IDR' => 
      array (
        'name' => 'Rupiah',
        'code' => '360',
        'entity' => 'Indonesia',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'Rp',
        'symbol_first' => true,
        'decimal_mark' => ',',
        'thousands_separator' => '.',
      ),
      'IRR' => 
      array (
        'name' => 'Iranian Rial',
        'code' => '364',
        'entity' => 'Iran',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '﷼',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'IQD' => 
      array (
        'name' => 'Iraqi Dinar',
        'code' => '368',
        'entity' => 'Iraq',
        'precision' => 3,
        'subunit' => 1000,
        'symbol' => 'ع.د',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'ILS' => 
      array (
        'name' => 'New Israeli Sheqel',
        'code' => '376',
        'entity' => 'Israel',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '₪',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'JMD' => 
      array (
        'name' => 'Jamaican Dollar',
        'code' => '388',
        'entity' => 'Jamaica',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'J$',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'JPY' => 
      array (
        'name' => 'Yen',
        'code' => '392',
        'entity' => 'Japan',
        'precision' => 0,
        'subunit' => 1,
        'symbol' => '¥',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'JOD' => 
      array (
        'name' => 'Jordanian Dinar',
        'code' => '400',
        'entity' => 'Jordan',
        'precision' => 3,
        'subunit' => 100,
        'symbol' => 'JD',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'KZT' => 
      array (
        'name' => 'Tenge',
        'code' => '398',
        'entity' => 'Kazakhstan',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '₸',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'KES' => 
      array (
        'name' => 'Kenyan Shilling',
        'code' => '404',
        'entity' => 'Kenya',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'KSh',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'KPW' => 
      array (
        'name' => 'North Korean Won',
        'code' => '408',
        'entity' => 'North Korea',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '₩',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'KRW' => 
      array (
        'name' => 'Won',
        'code' => '410',
        'entity' => 'South Korea',
        'precision' => 0,
        'subunit' => 1,
        'symbol' => '₩',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'KWD' => 
      array (
        'name' => 'Kuwaiti Dinar',
        'code' => '414',
        'entity' => 'Kuwait',
        'precision' => 3,
        'subunit' => 1000,
        'symbol' => 'KD',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'KGS' => 
      array (
        'name' => 'Som',
        'code' => '417',
        'entity' => 'Kyrgyzstan',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'лв',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'LAK' => 
      array (
        'name' => 'Lao Kip',
        'code' => '418',
        'entity' => 'Laos',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '₭',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'LBP' => 
      array (
        'name' => 'Lebanese Pound',
        'code' => '422',
        'entity' => 'Lebanon',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '£',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'LSL' => 
      array (
        'name' => 'Loti',
        'code' => '426',
        'entity' => 'Lesotho',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'M',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'ZAR' => 
      array (
        'name' => 'Rand',
        'code' => '710',
        'entity' => 'South Africa',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'R',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'LRD' => 
      array (
        'name' => 'Liberian Dollar',
        'code' => '430',
        'entity' => 'Liberia',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'LYD' => 
      array (
        'name' => 'Libyan Dinar',
        'code' => '434',
        'entity' => 'Libya',
        'precision' => 3,
        'subunit' => 1000,
        'symbol' => 'LD',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'CHF' => 
      array (
        'name' => 'Swiss Franc',
        'code' => '756',
        'entity' => 'Switzerland',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'Fr.',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => '\'',
      ),
      'MOP' => 
      array (
        'name' => 'Pataca',
        'code' => '446',
        'entity' => 'Macao',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'MOP$',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'MKD' => 
      array (
        'name' => 'Denar',
        'code' => '807',
        'entity' => 'North Macedonia',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'ден',
        'symbol_first' => false,
        'decimal_mark' => ',',
        'thousands_separator' => '',
      ),
      'MGA' => 
      array (
        'name' => 'Malagasy Ariary',
        'code' => '969',
        'entity' => 'Madagascar',
        'precision' => 2,
        'subunit' => 5,
        'symbol' => 'Ar',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'MWK' => 
      array (
        'name' => 'Malawi Kwacha',
        'code' => '454',
        'entity' => 'Malawi',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'MK',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'MYR' => 
      array (
        'name' => 'Malaysian Ringgit',
        'code' => '458',
        'entity' => 'Malaysia',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'RM',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'MVR' => 
      array (
        'name' => 'Rufiyaa',
        'code' => '462',
        'entity' => 'Maldives',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'Rf',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'MRU' => 
      array (
        'name' => 'Ouguiya',
        'code' => '929',
        'entity' => 'Mauritania',
        'precision' => 2,
        'subunit' => 5,
        'symbol' => 'UM',
        'symbol_first' => false,
        'decimal_mark' => ',',
        'thousands_separator' => '',
      ),
      'MUR' => 
      array (
        'name' => 'Mauritius Rupee',
        'code' => '480',
        'entity' => 'Mauritius',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '₨',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'MXN' => 
      array (
        'name' => 'Mexican Peso',
        'code' => '484',
        'entity' => 'Mexico',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'MDL' => 
      array (
        'name' => 'Moldovan Leu',
        'code' => '498',
        'entity' => 'Moldova',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'lei',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'MNT' => 
      array (
        'name' => 'Tugrik',
        'code' => '496',
        'entity' => 'Mongolia',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '₮',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'MAD' => 
      array (
        'name' => 'Moroccan Dirham',
        'code' => '504',
        'entity' => 'Morocco',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'د.م.',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'MZN' => 
      array (
        'name' => 'Mozambique Metical',
        'code' => '943',
        'entity' => 'Mozambique',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'MT',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'MMK' => 
      array (
        'name' => 'Kyat',
        'code' => '104',
        'entity' => 'Myanmar',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'K',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'NAD' => 
      array (
        'name' => 'Namibia Dollar',
        'code' => '516',
        'entity' => 'Namibia',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'NPR' => 
      array (
        'name' => 'Nepalese Rupee',
        'code' => '524',
        'entity' => 'Nepal',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '₨',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'NIO' => 
      array (
        'name' => 'Cordoba Oro',
        'code' => '558',
        'entity' => 'Nicaragua',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'C$',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'NGN' => 
      array (
        'name' => 'Naira',
        'code' => '566',
        'entity' => 'Nigeria',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '₦',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'OMR' => 
      array (
        'name' => 'Rial Omani',
        'code' => '512',
        'entity' => 'Oman',
        'precision' => 3,
        'subunit' => 1000,
        'symbol' => '﷼',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'PKR' => 
      array (
        'name' => 'Pakistan Rupee',
        'code' => '586',
        'entity' => 'Pakistan',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '₨',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'PAB' => 
      array (
        'name' => 'Balboa',
        'code' => '590',
        'entity' => 'Panama',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'B/.',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'PGK' => 
      array (
        'name' => 'Kina',
        'code' => '598',
        'entity' => 'Papua New Guinea',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'K',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'PYG' => 
      array (
        'name' => 'Guarani',
        'code' => '600',
        'entity' => 'Paraguay',
        'precision' => 0,
        'subunit' => 1,
        'symbol' => 'Gs',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'PEN' => 
      array (
        'name' => 'Sol',
        'code' => '604',
        'entity' => 'Peru',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'S/.',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'PHP' => 
      array (
        'name' => 'Philippine Peso',
        'code' => '608',
        'entity' => 'Philippines',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '₱',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'PLN' => 
      array (
        'name' => 'Zloty',
        'code' => '985',
        'entity' => 'Poland',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'zł',
        'symbol_first' => false,
        'decimal_mark' => ',',
        'thousands_separator' => ' ',
      ),
      'QAR' => 
      array (
        'name' => 'Qatari Rial',
        'code' => '634',
        'entity' => 'Qatar',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '﷼',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'RON' => 
      array (
        'name' => 'Romanian Leu',
        'code' => '946',
        'entity' => 'Romania',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'lei',
        'symbol_first' => true,
        'decimal_mark' => ',',
        'thousands_separator' => '.',
      ),
      'RUB' => 
      array (
        'name' => 'Russian Ruble',
        'code' => '643',
        'entity' => 'Russia',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '₽',
        'symbol_first' => false,
        'decimal_mark' => ',',
        'thousands_separator' => '',
      ),
      'RWF' => 
      array (
        'name' => 'Rwanda Franc',
        'code' => '646',
        'entity' => 'Rwanda',
        'precision' => 0,
        'subunit' => 1,
        'symbol' => 'R₣',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'SHP' => 
      array (
        'name' => 'Saint Helena Pound',
        'code' => '654',
        'entity' => 'Saint Helena',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '£',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'WST' => 
      array (
        'name' => 'Tala',
        'code' => '882',
        'entity' => 'Samoa',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'WS$',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'STN' => 
      array (
        'name' => 'Dobra',
        'code' => '930',
        'entity' => 'São Tomé and Príncipe',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'Db',
        'symbol_first' => false,
        'decimal_mark' => ',',
        'thousands_separator' => '',
      ),
      'SAR' => 
      array (
        'name' => 'Saudi Riyal',
        'code' => '682',
        'entity' => 'Saudi Arabia',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '﷼',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'RSD' => 
      array (
        'name' => 'Serbian Dinar',
        'code' => '941',
        'entity' => 'Serbia',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'дин.',
        'symbol_first' => false,
        'decimal_mark' => ',',
        'thousands_separator' => '.',
      ),
      'SCR' => 
      array (
        'name' => 'Seychelles Rupee',
        'code' => '690',
        'entity' => 'Seychelles',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '₨',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'SLL' => 
      array (
        'name' => 'Leone',
        'code' => '694',
        'entity' => 'Sierra Leone',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'Le',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'SLE' => 
      array (
        'name' => 'Leone',
        'code' => '925',
        'entity' => 'Sierra Leone',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'Le',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'SGD' => 
      array (
        'name' => 'Singapore Dollar',
        'code' => '702',
        'entity' => 'Singapore',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'S$',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'SBD' => 
      array (
        'name' => 'Solomon Islands Dollar',
        'code' => '090',
        'entity' => 'Solomon Islands',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'SOS' => 
      array (
        'name' => 'Somali Shilling',
        'code' => '706',
        'entity' => 'Somalia',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'S',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'SSP' => 
      array (
        'name' => 'South Sudanese Pound',
        'code' => '728',
        'entity' => 'South Sudan',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '£',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'LKR' => 
      array (
        'name' => 'Sri Lanka Rupee',
        'code' => '144',
        'entity' => 'Sri Lanka',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '₨',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'SDG' => 
      array (
        'name' => 'Sudanese Pound',
        'code' => '938',
        'entity' => 'Sudan',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'ج.س.',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'SRD' => 
      array (
        'name' => 'Surinam Dollar',
        'code' => '968',
        'entity' => 'Suriname',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'SEK' => 
      array (
        'name' => 'Swedish Krona',
        'code' => '752',
        'entity' => 'Sweden',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'kr',
        'symbol_first' => false,
        'decimal_mark' => ',',
        'thousands_separator' => '',
      ),
      'SYP' => 
      array (
        'name' => 'Syrian Pound',
        'code' => '760',
        'entity' => 'Syria',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '£S',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'TWD' => 
      array (
        'name' => 'New Taiwan Dollar',
        'code' => '901',
        'entity' => 'Taiwan',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'NT$',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'TJS' => 
      array (
        'name' => 'Somoni',
        'code' => '972',
        'entity' => 'Tajikistan',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'SM',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'TZS' => 
      array (
        'name' => 'Tanzanian Shilling',
        'code' => '834',
        'entity' => 'Tanzania',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'TSh',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'THB' => 
      array (
        'name' => 'Baht',
        'code' => '764',
        'entity' => 'Thailand',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '฿',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'TOP' => 
      array (
        'name' => 'Pa’anga',
        'code' => '776',
        'entity' => 'Tonga',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'T$',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'TTD' => 
      array (
        'name' => 'Trinidad and Tobago Dollar',
        'code' => '780',
        'entity' => 'Trinidad and Tobago',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'TT$',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'TND' => 
      array (
        'name' => 'Tunisian Dinar',
        'code' => '788',
        'entity' => 'Tunisia',
        'precision' => 3,
        'subunit' => 1000,
        'symbol' => 'د.ت',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'TRY' => 
      array (
        'name' => 'Turkish Lira',
        'code' => '949',
        'entity' => 'Turkey',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '₺',
        'symbol_first' => true,
        'decimal_mark' => ',',
        'thousands_separator' => '.',
      ),
      'TMT' => 
      array (
        'name' => 'Turkmenistan New Manat',
        'code' => '934',
        'entity' => 'Turkmenistan',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'T',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'UGX' => 
      array (
        'name' => 'Uganda Shilling',
        'code' => '800',
        'entity' => 'Uganda',
        'precision' => 0,
        'subunit' => 1,
        'symbol' => 'USh',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'UAH' => 
      array (
        'name' => 'Hryvnia',
        'code' => '980',
        'entity' => 'Ukraine',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '₴',
        'symbol_first' => false,
        'decimal_mark' => ',',
        'thousands_separator' => '',
      ),
      'AED' => 
      array (
        'name' => 'UAE Dirham',
        'code' => '784',
        'entity' => 'United Arab Emirates',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'د.إ',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'UYU' => 
      array (
        'name' => 'Peso Uruguayo',
        'code' => '858',
        'entity' => 'Uruguay',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$U',
        'symbol_first' => true,
        'decimal_mark' => ',',
        'thousands_separator' => '.',
      ),
      'UZS' => 
      array (
        'name' => 'Uzbekistan Sum',
        'code' => '860',
        'entity' => 'Uzbekistan',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'лв',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'VUV' => 
      array (
        'name' => 'Vatu',
        'code' => '548',
        'entity' => 'Vanuatu',
        'precision' => 0,
        'subunit' => 1,
        'symbol' => 'VT',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'VES' => 
      array (
        'name' => 'Bolívar Soberano',
        'code' => '928',
        'entity' => 'Venezuela',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'Bs.S',
        'symbol_first' => true,
        'decimal_mark' => ',',
        'thousands_separator' => '',
      ),
      'VED' => 
      array (
        'name' => 'Bolívar Soberano',
        'code' => '926',
        'entity' => 'Venezuela',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'Bs.',
        'symbol_first' => true,
        'decimal_mark' => ',',
        'thousands_separator' => '',
      ),
      'VND' => 
      array (
        'name' => 'Dong',
        'code' => '704',
        'entity' => 'Vietnam',
        'precision' => 0,
        'subunit' => 1,
        'symbol' => '₫',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'YER' => 
      array (
        'name' => 'Yemeni Rial',
        'code' => '886',
        'entity' => 'Yemen',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '﷼',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'ZMW' => 
      array (
        'name' => 'Zambian Kwacha',
        'code' => '967',
        'entity' => 'Zambia',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => 'ZK',
        'symbol_first' => false,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
      'ZWL' => 
      array (
        'name' => 'Zimbabwe Dollar',
        'code' => '932',
        'entity' => 'Zimbabwe',
        'precision' => 2,
        'subunit' => 100,
        'symbol' => '$',
        'symbol_first' => true,
        'decimal_mark' => '.',
        'thousands_separator' => ',',
      ),
    ),
  ),
  'plaid' => 
  array (
    'client_id' => '',
    'client_secret' => '',
    'environment' => 'sandbox',
    'webhook_url' => '',
  ),
  'queue' => 
  array (
    'default' => 'database',
    'connections' => 
    array (
      'sync' => 
      array (
        'driver' => 'sync',
      ),
      'database' => 
      array (
        'driver' => 'database',
        'connection' => NULL,
        'table' => 'jobs',
        'queue' => 'default',
        'retry_after' => 90,
        'after_commit' => true,
      ),
      'beanstalkd' => 
      array (
        'driver' => 'beanstalkd',
        'host' => 'localhost',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => 0,
        'after_commit' => true,
      ),
      'sqs' => 
      array (
        'driver' => 'sqs',
        'key' => '',
        'secret' => '',
        'prefix' => 'https://sqs.us-east-1.amazonaws.com/your-account-id',
        'queue' => 'default',
        'suffix' => NULL,
        'region' => 'us-east-1',
        'after_commit' => true,
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => NULL,
        'after_commit' => true,
      ),
    ),
    'batching' => 
    array (
      'database' => 'mysql',
      'table' => 'job_batches',
    ),
    'failed' => 
    array (
      'driver' => 'database-uuids',
      'database' => 'mysql',
      'table' => 'failed_jobs',
    ),
  ),
  'sanctum' => 
  array (
    'stateful' => 
    array (
      0 => 'localhost',
      1 => 'localhost:3000',
      2 => '127.0.0.1',
      3 => '127.0.0.1:8000',
      4 => '::1',
      5 => 'localhost:8000',
    ),
    'guard' => 
    array (
      0 => 'web',
    ),
    'expiration' => NULL,
    'token_prefix' => '',
    'middleware' => 
    array (
      'authenticate_session' => 'Laravel\\Sanctum\\Http\\Middleware\\AuthenticateSession',
      'encrypt_cookies' => 'Illuminate\\Cookie\\Middleware\\EncryptCookies',
      'validate_csrf_token' => 'Illuminate\\Foundation\\Http\\Middleware\\ValidateCsrfToken',
    ),
  ),
  'services' => 
  array (
    'postmark' => 
    array (
      'token' => NULL,
    ),
    'resend' => 
    array (
      'key' => NULL,
    ),
    'ses' => 
    array (
      'key' => '',
      'secret' => '',
      'region' => 'us-east-1',
    ),
    'slack' => 
    array (
      'notifications' => 
      array (
        'bot_user_oauth_token' => NULL,
        'channel' => NULL,
      ),
    ),
    'github' => 
    array (
      'client_id' => '',
      'client_secret' => '',
      'redirect' => 'http://localhost:8000/company/oauth/github/callback',
    ),
    'currency_api' => 
    array (
      'key' => '',
      'base_url' => 'https://v6.exchangerate-api.com/v6',
    ),
    'plaid' => 
    array (
      'client_id' => '',
      'client_secret' => '',
      'environment' => 'sandbox',
    ),
    'whatsapp' => 
    array (
      'api_url' => 'https://graph.facebook.com/v18.0',
      'access_token' => '',
      'phone_number_id' => '',
      'webhook_verify_token' => '',
    ),
  ),
  'session' => 
  array (
    'driver' => 'file',
    'lifetime' => 120,
    'expire_on_close' => false,
    'encrypt' => false,
    'files' => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\storage\\framework/sessions',
    'connection' => NULL,
    'table' => 'sessions',
    'store' => NULL,
    'lottery' => 
    array (
      0 => 2,
      1 => 100,
    ),
    'cookie' => 'arena-doviz-session',
    'path' => '/',
    'domain' => NULL,
    'secure' => NULL,
    'http_only' => true,
    'same_site' => 'lax',
    'partitioned' => false,
  ),
  'snappy' => 
  array (
    'pdf' => 
    array (
      'enabled' => true,
      'binary' => 'C:\\ProgramData\\chocolatey\\bin\\wkhtmltopdf.exe',
      'timeout' => false,
      'options' => 
      array (
        'no-pdf-compression' => true,
        'disable-javascript' => true,
        'margin-top' => '10mm',
        'margin-right' => '7.5mm',
        'margin-bottom' => '15mm',
        'margin-left' => '7.5mm',
        'page-size' => 'Letter',
        'footer-right' => 'Page [page] / [toPage]',
        'footer-font-size' => '8',
        'footer-spacing' => '5',
        'zoom' => '1.3',
      ),
      'env' => 
      array (
      ),
    ),
    'image' => 
    array (
      'enabled' => true,
      'binary' => '/usr/local/bin/wkhtmltoimage',
      'timeout' => false,
      'options' => 
      array (
      ),
      'env' => 
      array (
      ),
    ),
  ),
  'transmatic' => 
  array (
    'translator' => 
    array (
      'default' => 'Wallo\\Transmatic\\Services\\Translators\\AwsTranslate',
      'timeout' => 30,
      'placeholder_format' => '#placeholder',
      'supports_placeholders' => true,
    ),
    'source_locale' => 'en',
    'storage' => 'file',
    'cache' => 
    array (
      'key' => 'translations',
      'duration' => 30,
    ),
    'file' => 
    array (
      'path' => 'resources/data/lang',
    ),
    'job' => 
    array (
      'chunk_size' => 200,
      'max_attempts' => 3,
      'retry_duration' => 60,
    ),
    'batching' => 
    array (
      'name' => 'TransmaticBatch',
      'connection' => 'database',
      'queue' => 'translations',
      'allow_failures' => true,
    ),
  ),
  'blade-heroicons' => 
  array (
    'prefix' => 'heroicon',
    'fallback' => '',
    'class' => '',
    'attributes' => 
    array (
    ),
  ),
  'flare' => 
  array (
    'key' => NULL,
    'flare_middleware' => 
    array (
      0 => 'Spatie\\FlareClient\\FlareMiddleware\\RemoveRequestIp',
      1 => 'Spatie\\FlareClient\\FlareMiddleware\\AddGitInformation',
      2 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddNotifierName',
      3 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddEnvironmentInformation',
      4 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddExceptionInformation',
      5 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddDumps',
      'Spatie\\LaravelIgnition\\FlareMiddleware\\AddLogs' => 
      array (
        'maximum_number_of_collected_logs' => 200,
      ),
      'Spatie\\LaravelIgnition\\FlareMiddleware\\AddQueries' => 
      array (
        'maximum_number_of_collected_queries' => 200,
        'report_query_bindings' => true,
      ),
      'Spatie\\LaravelIgnition\\FlareMiddleware\\AddJobs' => 
      array (
        'max_chained_job_reporting_depth' => 5,
      ),
      6 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddContext',
      7 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddExceptionHandledStatus',
      'Spatie\\FlareClient\\FlareMiddleware\\CensorRequestBodyFields' => 
      array (
        'censor_fields' => 
        array (
          0 => 'password',
          1 => 'password_confirmation',
        ),
      ),
      'Spatie\\FlareClient\\FlareMiddleware\\CensorRequestHeaders' => 
      array (
        'headers' => 
        array (
          0 => 'API-KEY',
          1 => 'Authorization',
          2 => 'Cookie',
          3 => 'Set-Cookie',
          4 => 'X-CSRF-TOKEN',
          5 => 'X-XSRF-TOKEN',
        ),
      ),
    ),
    'send_logs_as_events' => true,
  ),
  'ignition' => 
  array (
    'editor' => 'phpstorm',
    'theme' => 'auto',
    'enable_share_button' => true,
    'register_commands' => false,
    'solution_providers' => 
    array (
      0 => 'Spatie\\Ignition\\Solutions\\SolutionProviders\\BadMethodCallSolutionProvider',
      1 => 'Spatie\\Ignition\\Solutions\\SolutionProviders\\MergeConflictSolutionProvider',
      2 => 'Spatie\\Ignition\\Solutions\\SolutionProviders\\UndefinedPropertySolutionProvider',
      3 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\IncorrectValetDbCredentialsSolutionProvider',
      4 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingAppKeySolutionProvider',
      5 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\DefaultDbNameSolutionProvider',
      6 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\TableNotFoundSolutionProvider',
      7 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingImportSolutionProvider',
      8 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\InvalidRouteActionSolutionProvider',
      9 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\ViewNotFoundSolutionProvider',
      10 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\RunningLaravelDuskInProductionProvider',
      11 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingColumnSolutionProvider',
      12 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\UnknownValidationSolutionProvider',
      13 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingMixManifestSolutionProvider',
      14 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingViteManifestSolutionProvider',
      15 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingLivewireComponentSolutionProvider',
      16 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\UndefinedViewVariableSolutionProvider',
      17 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\GenericLaravelExceptionSolutionProvider',
      18 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\OpenAiSolutionProvider',
      19 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\SailNetworkSolutionProvider',
      20 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\UnknownMysql8CollationSolutionProvider',
      21 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\UnknownMariadbCollationSolutionProvider',
    ),
    'ignored_solution_providers' => 
    array (
    ),
    'enable_runnable_solutions' => NULL,
    'remote_sites_path' => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas',
    'local_sites_path' => '',
    'housekeeping_endpoint_prefix' => '_ignition',
    'settings_file_path' => '',
    'recorders' => 
    array (
      0 => 'Spatie\\LaravelIgnition\\Recorders\\DumpRecorder\\DumpRecorder',
      1 => 'Spatie\\LaravelIgnition\\Recorders\\JobRecorder\\JobRecorder',
      2 => 'Spatie\\LaravelIgnition\\Recorders\\LogRecorder\\LogRecorder',
      3 => 'Spatie\\LaravelIgnition\\Recorders\\QueryRecorder\\QueryRecorder',
    ),
    'open_ai_key' => NULL,
    'with_stack_frame_arguments' => true,
    'argument_reducers' => 
    array (
      0 => 'Spatie\\Backtrace\\Arguments\\Reducers\\BaseTypeArgumentReducer',
      1 => 'Spatie\\Backtrace\\Arguments\\Reducers\\ArrayArgumentReducer',
      2 => 'Spatie\\Backtrace\\Arguments\\Reducers\\StdClassArgumentReducer',
      3 => 'Spatie\\Backtrace\\Arguments\\Reducers\\EnumArgumentReducer',
      4 => 'Spatie\\Backtrace\\Arguments\\Reducers\\ClosureArgumentReducer',
      5 => 'Spatie\\Backtrace\\Arguments\\Reducers\\DateTimeArgumentReducer',
      6 => 'Spatie\\Backtrace\\Arguments\\Reducers\\DateTimeZoneArgumentReducer',
      7 => 'Spatie\\Backtrace\\Arguments\\Reducers\\SymphonyRequestArgumentReducer',
      8 => 'Spatie\\LaravelIgnition\\ArgumentReducers\\ModelArgumentReducer',
      9 => 'Spatie\\LaravelIgnition\\ArgumentReducers\\CollectionArgumentReducer',
      10 => 'Spatie\\Backtrace\\Arguments\\Reducers\\StringableArgumentReducer',
    ),
  ),
  'squire' => 
  array (
    'cache-path' => 'C:\\Users\\<USER>\\Documents\\arenadoviz\\erpsaas\\storage\\framework/cache',
    'cache-prefix' => 'squire',
  ),
  'tinker' => 
  array (
    'commands' => 
    array (
    ),
    'alias' => 
    array (
    ),
    'dont_alias' => 
    array (
      0 => 'App\\Nova',
    ),
  ),
);
