<div class="rounded-lg bg-primary-300/10 shadow-sm ring-1 ring-gray-950/10 dark:ring-white/20 overflow-hidden">
    <div class="flex items-center p-4 gap-x-2">
        <?php if($connectedBankAccount->institution->logo_url): ?>
            <img src="<?php echo e($connectedBankAccount->institution->logo_url); ?>" alt="<?php echo e($connectedBankAccount->institution->name); ?>" class="h-10">
        <?php else: ?>
            <div class="flex-shrink-0 bg-platinum p-2 rounded-full dark:bg-gray-500/20">
                <?php if (isset($component)) { $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.icon','data' => ['icon' => 'heroicon-o-building-library','class' => 'h-6 w-6 text-gray-500 dark:text-gray-400']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'heroicon-o-building-library','class' => 'h-6 w-6 text-gray-500 dark:text-gray-400']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $attributes = $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $component = $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
            </div>
        <?php endif; ?>
        <div>
            <p class="text-sm font-medium leading-6 text-gray-900 dark:text-white"><?php echo e($connectedBankAccount->institution->name); ?></p>
            <p class="text-sm leading-6 text-gray-600 dark:text-gray-200"><?php echo e(ucwords($connectedBankAccount->subtype)); ?> <?php echo e($connectedBankAccount->masked_number); ?></p>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\actions\transaction-import-modal.blade.php ENDPATH**/ ?>