<?php $__env->startSection('content'); ?>
    <div class="header">
        <div class="title"><?php echo e($report->getTitle()); ?></div>
        <div class="company-name"><?php echo e($company->name); ?></div>
        <?php if($startDate && $endDate): ?>
            <div class="date-range">Date Range: <?php echo e($startDate); ?> to <?php echo e($endDate); ?></div>
        <?php else: ?>
            <div class="date-range">As of <?php echo e($endDate); ?></div>
        <?php endif; ?>
    </div>
    <table class="table-class">
        <colgroup>
            <col span="1" style="width: 65%;">
            <col span="1" style="width: 35%;">
        </colgroup>
        <thead class="table-head">
        <tr>
            <?php $__currentLoopData = $report->getSummaryHeaders(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $header): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <th class="<?php echo e($report->getAlignmentClass($index)); ?>">
                    <?php echo e($header); ?>

                </th>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tr>
        </thead>
        <?php $__currentLoopData = $report->getSummaryCategories(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tbody>
            <tr>
                <?php $__currentLoopData = $category->summary; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $cell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <td class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                        $report->getAlignmentClass($index),
                    ]); ?>"
                    >
                        <?php echo e($cell); ?>

                    </td>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tr>

            <?php if($category->summary['account_name'] === 'Cost of Goods Sold'): ?>
                <tr class="category-header-row">
                    <?php $__currentLoopData = $report->getGrossProfit(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $grossProfitIndex => $grossProfitCell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <td class="<?php echo e($report->getAlignmentClass($grossProfitIndex)); ?>">
                            <?php echo e($grossProfitCell); ?>

                        </td>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tr>
            <?php endif; ?>
            </tbody>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <tfoot>
        <tr class="category-header-row">
            <?php $__currentLoopData = $report->getOverallTotals(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $total): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <td class="<?php echo e($report->getAlignmentClass($index)); ?>">
                    <?php echo e($total); ?>

                </td>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tr>
        </tfoot>
    </table>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('components.company.reports.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\company\reports\income-statement-summary-pdf.blade.php ENDPATH**/ ?>