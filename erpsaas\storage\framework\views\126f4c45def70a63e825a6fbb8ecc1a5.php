<?php
    use App\Models\Accounting\Bill;
    use App\Filament\Company\Resources\Accounting\TransactionResource;
    use App\Filament\Company\Resources\Purchases\BillResource\Pages\ViewBill;
    use App\Filament\Company\Resources\Sales\InvoiceResource\Pages\ViewInvoice;

    $iconPosition = \Filament\Support\Enums\IconPosition::After;
?>

<table class="w-full table-auto min-w-[50rem] divide-y divide-gray-200 dark:divide-white/5">
    <?php if (isset($component)) { $__componentOriginal088d9df2e25f0de01ebf6280a5631361 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal088d9df2e25f0de01ebf6280a5631361 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.tables.header','data' => ['headers' => $report->getHeaders(),'alignmentClass' => [$report, 'getAlignmentClass']]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.tables.header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['headers' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($report->getHeaders()),'alignmentClass' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([$report, 'getAlignmentClass'])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal088d9df2e25f0de01ebf6280a5631361)): ?>
<?php $attributes = $__attributesOriginal088d9df2e25f0de01ebf6280a5631361; ?>
<?php unset($__attributesOriginal088d9df2e25f0de01ebf6280a5631361); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal088d9df2e25f0de01ebf6280a5631361)): ?>
<?php $component = $__componentOriginal088d9df2e25f0de01ebf6280a5631361; ?>
<?php unset($__componentOriginal088d9df2e25f0de01ebf6280a5631361); ?>
<?php endif; ?>
    <?php $__currentLoopData = $report->getCategories(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $categoryIndex => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <tbody class="divide-y divide-gray-200 dark:divide-white/5">
        <!-- Category Header -->
        <tr class="bg-gray-50 dark:bg-white/5">
            <?php if (isset($component)) { $__componentOriginal0582040fe960eff09c1461f7f86a8187 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0582040fe960eff09c1461f7f86a8187 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-tables::components.cell','data' => ['tag' => 'th','colspan' => ''.e(count($report->getHeaders())).'','class' => 'text-left']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-tables::cell'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['tag' => 'th','colspan' => ''.e(count($report->getHeaders())).'','class' => 'text-left']); ?>
                <div class="px-3 py-3.5">
                    <?php $__currentLoopData = $category->header; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $headerRow): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div
                            class="text-sm <?php echo e($loop->first ? 'font-semibold text-gray-950 dark:text-white' : 'font-normal text-gray-500 dark:text-white/50'); ?>">
                            <?php $__currentLoopData = $headerRow; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $headerValue): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if(!empty($headerValue)): ?>
                                    <?php echo e($headerValue); ?>

                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0582040fe960eff09c1461f7f86a8187)): ?>
<?php $attributes = $__attributesOriginal0582040fe960eff09c1461f7f86a8187; ?>
<?php unset($__attributesOriginal0582040fe960eff09c1461f7f86a8187); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0582040fe960eff09c1461f7f86a8187)): ?>
<?php $component = $__componentOriginal0582040fe960eff09c1461f7f86a8187; ?>
<?php unset($__componentOriginal0582040fe960eff09c1461f7f86a8187); ?>
<?php endif; ?>
        </tr>
        <!-- Transactions Data -->
        <?php $__currentLoopData = $category->data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dataIndex => $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr
                class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                    'bg-gray-50 dark:bg-white/5' => $loop->first || $loop->last || $loop->remaining === 1,
                ]); ?>"
            >
                <?php $__currentLoopData = $transaction; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cellIndex => $cell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if (isset($component)) { $__componentOriginal0582040fe960eff09c1461f7f86a8187 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0582040fe960eff09c1461f7f86a8187 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-tables::components.cell','data' => ['class' => \Illuminate\Support\Arr::toCssClasses([
                           $report->getAlignmentClass($cellIndex),
                           'whitespace-normal' => $cellIndex === 1,
                       ])]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-tables::cell'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(\Illuminate\Support\Arr::toCssClasses([
                           $report->getAlignmentClass($cellIndex),
                           'whitespace-normal' => $cellIndex === 1,
                       ]))]); ?>
                        <div
                            class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                                'px-3 py-4 text-sm leading-6 text-gray-950 dark:text-white',
                                'font-semibold' => $loop->parent->first || $loop->parent->last || $loop->parent->remaining === 1,
                            ]); ?>"
                        >
                            <?php if(is_array($cell) && isset($cell['description'])): ?>
                                <?php if(isset($cell['id']) && isset($cell['url'])): ?>
                                    <?php if (isset($component)) { $__componentOriginal549c94d872270b69c72bdf48cb183bc9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal549c94d872270b69c72bdf48cb183bc9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.link','data' => ['href' => $cell['url'],'target' => '_blank','color' => 'primary','icon' => 'heroicon-o-arrow-top-right-on-square','iconPosition' => $iconPosition,'iconSize' => 'w-4 h-4 min-w-4 min-h-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($cell['url']),'target' => '_blank','color' => 'primary','icon' => 'heroicon-o-arrow-top-right-on-square','icon-position' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($iconPosition),'icon-size' => 'w-4 h-4 min-w-4 min-h-4']); ?>
                                        <?php echo e($cell['description']); ?>

                                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal549c94d872270b69c72bdf48cb183bc9)): ?>
<?php $attributes = $__attributesOriginal549c94d872270b69c72bdf48cb183bc9; ?>
<?php unset($__attributesOriginal549c94d872270b69c72bdf48cb183bc9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal549c94d872270b69c72bdf48cb183bc9)): ?>
<?php $component = $__componentOriginal549c94d872270b69c72bdf48cb183bc9; ?>
<?php unset($__componentOriginal549c94d872270b69c72bdf48cb183bc9); ?>
<?php endif; ?>
                                <?php else: ?>
                                    <?php echo e($cell['description']); ?>

                                <?php endif; ?>
                            <?php else: ?>
                                <?php echo e($cell); ?>

                            <?php endif; ?>
                        </div>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0582040fe960eff09c1461f7f86a8187)): ?>
<?php $attributes = $__attributesOriginal0582040fe960eff09c1461f7f86a8187; ?>
<?php unset($__attributesOriginal0582040fe960eff09c1461f7f86a8187); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0582040fe960eff09c1461f7f86a8187)): ?>
<?php $component = $__componentOriginal0582040fe960eff09c1461f7f86a8187; ?>
<?php unset($__componentOriginal0582040fe960eff09c1461f7f86a8187); ?>
<?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tr>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <!-- Spacer Row -->
        <?php if (! ($loop->last)): ?>
            <tr>
                <td colspan="<?php echo e(count($report->getHeaders())); ?>">
                    <div class="min-h-12"></div>
                </td>
            </tr>
        <?php endif; ?>
        </tbody>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</table>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\company\tables\reports\account-transactions.blade.php ENDPATH**/ ?>