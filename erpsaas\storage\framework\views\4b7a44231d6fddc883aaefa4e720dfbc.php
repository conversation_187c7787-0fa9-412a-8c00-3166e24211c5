<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'preview' => false,
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'preview' => false,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<div
    class="<?php echo \Illuminate\Support\Arr::toCssClasses([
        'doc-template-container flex justify-center',
    ]); ?>"
>
    <div class="max-w-full overflow-x-auto shadow-xl ring-1 ring-gray-950/5 dark:ring-white/10">
        <div
            class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                'doc-template-paper bg-[#ffffff] overflow-y-auto',
                'w-[51.25rem] h-[64rem]' => ! $preview,
                'w-[48rem] min-h-[61.75rem] preview' => $preview,
            ]); ?>"
        >
            <?php echo e($slot); ?>

        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\company\document-template\container.blade.php ENDPATH**/ ?>