<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'errorMessage' => null,
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'errorMessage' => null,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<div class="mt-6 filament-companies-socialite">
    <div class="filament-companies-socialite-divider flex flex-row items-center justify-between py-4 text-gray-900 dark:text-white">
        <hr class="w-full mr-2">
        <?php echo e(__('filament-companies::default.subheadings.auth.login')); ?>

        <hr class="w-full ml-2">
    </div>

    <?php if($errorMessage): ?>
        <div class="mt-6 text-center text-sm text-danger-600 dark:text-danger-500"><?php echo $errorMessage; ?></div>
    <?php endif; ?>

    <div class="filament-companies-socialite-button-container mt-6 flex flex-wrap items-center justify-center gap-6">
        <?php $__currentLoopData = \Wallo\FilamentCompanies\Enums\Provider::cases(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $provider): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if($provider->isEnabled()): ?>
                <a href="<?php echo e(\Wallo\FilamentCompanies\FilamentCompanies::generateOAuthRedirectUrl($provider->value)); ?>"
                   class="filament-companies-socialite-buttons inline-flex rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 dark:focus:border-primary-500 py-2 px-4 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600">
                    <span class="sr-only"><?php echo e($provider->getLabel()); ?></span>
                    <div class="h-6 w-6">
                        <?php echo e($provider->getIconView()); ?>

                    </div>
                </a>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>

<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\vendor\andrewdwallo\filament-companies\resources\views\components\socialite.blade.php ENDPATH**/ ?>