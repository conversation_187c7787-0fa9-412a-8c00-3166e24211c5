<?php
    $modals = \Wallo\FilamentCompanies\FilamentCompanies::getModals();
?>

<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <?php echo e($this->table); ?>


    <?php if (isset($component)) { $__componentOriginal0942a211c37469064369f887ae8d1cef = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0942a211c37469064369f887ae8d1cef = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.modal.index','data' => ['id' => 'displayingToken','icon' => 'heroicon-o-key','iconColor' => 'primary','alignment' => ''.e($modals['alignment']).'','footerActionsAlignment' => ''.e($modals['formActionsAlignment']).'','width' => ''.e($modals['width']).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'displayingToken','icon' => 'heroicon-o-key','icon-color' => 'primary','alignment' => ''.e($modals['alignment']).'','footer-actions-alignment' => ''.e($modals['formActionsAlignment']).'','width' => ''.e($modals['width']).'']); ?>
         <?php $__env->slot('heading', null, []); ?> 
            <?php echo e(__('filament-companies::default.modal_titles.token')); ?>

         <?php $__env->endSlot(); ?>

         <?php $__env->slot('description', null, []); ?> 
            <div>
                <?php echo e(__('filament-companies::default.modal_descriptions.copy_token')); ?>

            </div>
         <?php $__env->endSlot(); ?>

        <?php if (isset($component)) { $__componentOriginalf107b8fc45f22b612c8feabe50c99223 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf107b8fc45f22b612c8feabe50c99223 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-companies::components.input','data' => ['xRef' => 'plaintextToken','type' => 'text','readonly' => 'readonly','value' => $plainTextToken,'class' => 'mt-4 bg-gray-100 dark:bg-gray-800 px-4 py-2 rounded font-mono text-sm text-gray-500 w-full break-all','autocomplete' => 'off','autocorrect' => 'off','autocapitalize' => 'off','spellcheck' => 'false','@showingTokenModal.window' => 'setTimeout(() => $refs.plaintextToken.select(), 250)']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-companies::input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['x-ref' => 'plaintextToken','type' => 'text','readonly' => 'readonly','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($plainTextToken),'class' => 'mt-4 bg-gray-100 dark:bg-gray-800 px-4 py-2 rounded font-mono text-sm text-gray-500 w-full break-all','autocomplete' => 'off','autocorrect' => 'off','autocapitalize' => 'off','spellcheck' => 'false','@showing-token-modal.window' => 'setTimeout(() => $refs.plaintextToken.select(), 250)']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf107b8fc45f22b612c8feabe50c99223)): ?>
<?php $attributes = $__attributesOriginalf107b8fc45f22b612c8feabe50c99223; ?>
<?php unset($__attributesOriginalf107b8fc45f22b612c8feabe50c99223); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf107b8fc45f22b612c8feabe50c99223)): ?>
<?php $component = $__componentOriginalf107b8fc45f22b612c8feabe50c99223; ?>
<?php unset($__componentOriginalf107b8fc45f22b612c8feabe50c99223); ?>
<?php endif; ?>

        <?php if($modals['cancelButtonAction']): ?>
             <?php $__env->slot('footerActions', null, []); ?> 
                <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['color' => 'gray','wire:click' => 'cancelDisplayingToken']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['color' => 'gray','wire:click' => 'cancelDisplayingToken']); ?>
                    <?php echo e(__('filament-companies::default.buttons.close')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
             <?php $__env->endSlot(); ?>
        <?php endif; ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0942a211c37469064369f887ae8d1cef)): ?>
<?php $attributes = $__attributesOriginal0942a211c37469064369f887ae8d1cef; ?>
<?php unset($__attributesOriginal0942a211c37469064369f887ae8d1cef); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0942a211c37469064369f887ae8d1cef)): ?>
<?php $component = $__componentOriginal0942a211c37469064369f887ae8d1cef; ?>
<?php unset($__componentOriginal0942a211c37469064369f887ae8d1cef); ?>
<?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\vendor\andrewdwallo\filament-companies\resources\views\filament\pages\user\personal-access-tokens.blade.php ENDPATH**/ ?>