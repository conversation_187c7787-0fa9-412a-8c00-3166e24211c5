<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'headers',
    'alignmentClass',
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'headers',
    'alignmentClass',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<thead class="divide-y divide-gray-200 dark:divide-white/5 whitespace-nowrap">
<tr class="bg-gray-50 dark:bg-white/5">
    <?php $__currentLoopData = $headers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $headerIndex => $headerCell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <th class="px-3 py-3.5 sm:first-of-type:ps-6 sm:last-of-type:pe-6 <?php echo e($alignmentClass($headerIndex)); ?>">
            <span class="text-sm font-semibold leading-6 text-gray-950 dark:text-white">
                <?php echo e($headerCell); ?>

            </span>
        </th>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</tr>
</thead>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\company\tables\header.blade.php ENDPATH**/ ?>