<div class="space-y-6">
    <!-- Client Overview -->
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('heading', null, []); ?> 
            <div class="flex items-center gap-2">
                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-users'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                Client Profitability Report
            </div>
         <?php $__env->endSlot(); ?>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg text-center">
                <div class="text-blue-600 dark:text-blue-400 text-sm font-medium">Total Clients</div>
                <div class="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    <?php echo e(number_format($data['summary']['total_clients'])); ?>

                </div>
            </div>
            
            <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg text-center">
                <div class="text-green-600 dark:text-green-400 text-sm font-medium">Total Commission</div>
                <div class="text-2xl font-bold text-green-900 dark:text-green-100">
                    <?php echo e(number_format($data['summary']['total_commission'], 2)); ?> TRY
                </div>
            </div>
            
            <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg text-center">
                <div class="text-yellow-600 dark:text-yellow-400 text-sm font-medium">Avg Commission per Client</div>
                <div class="text-2xl font-bold text-yellow-900 dark:text-yellow-100">
                    <?php echo e(number_format($data['summary']['avg_commission_per_client'], 2)); ?> TRY
                </div>
            </div>
            
            <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg text-center">
                <div class="text-purple-600 dark:text-purple-400 text-sm font-medium">Client Segments</div>
                <div class="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    <?php echo e(count($data['client_segments'])); ?>

                </div>
            </div>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>

    <!-- Top Clients by Commission -->
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('heading', null, []); ?> Top 20 Clients by Commission <?php $__env->endSlot(); ?>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-800">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Rank
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Client Name
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Total Commission (TRY)
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Transaction Count
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Total Volume (TRY)
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Avg Commission
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            First Transaction
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Last Transaction
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    <?php $__currentLoopData = $data['top_clients']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="<?php echo e($index < 3 ? 'bg-yellow-50 dark:bg-yellow-900/10' : ''); ?>">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                                <?php if($index === 0): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        🥇 #<?php echo e($index + 1); ?>

                                    </span>
                                <?php elseif($index === 1): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        🥈 #<?php echo e($index + 1); ?>

                                    </span>
                                <?php elseif($index === 2): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                        🥉 #<?php echo e($index + 1); ?>

                                    </span>
                                <?php else: ?>
                                    #<?php echo e($index + 1); ?>

                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                                <?php echo e($client->client->name ?? 'Unknown Client'); ?>

                                <?php if($client->client->email): ?>
                                    <div class="text-xs text-gray-500"><?php echo e($client->client->email); ?></div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400 font-semibold">
                                <?php echo e(number_format($client->total_commission, 2)); ?>

                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                <?php echo e(number_format($client->transaction_count)); ?>

                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                <?php echo e(number_format($client->total_volume, 2)); ?>

                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 dark:text-blue-400">
                                <?php echo e(number_format($client->avg_commission, 2)); ?>

                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                <?php echo e(\Carbon\Carbon::parse($client->first_transaction)->format('M d, Y')); ?>

                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                <?php echo e(\Carbon\Carbon::parse($client->last_transaction)->format('M d, Y')); ?>

                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>

    <!-- Client Segmentation -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
             <?php $__env->slot('heading', null, []); ?> Client Value Segments <?php $__env->endSlot(); ?>
            
            <div class="space-y-4">
                <div class="flex justify-between items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div>
                        <div class="font-semibold text-green-900 dark:text-green-100">High Value Clients</div>
                        <div class="text-sm text-green-600 dark:text-green-400">Above 2x average commission</div>
                    </div>
                    <div class="text-2xl font-bold text-green-900 dark:text-green-100">
                        <?php echo e(number_format($data['client_segments']['high_value'])); ?>

                    </div>
                </div>
                
                <div class="flex justify-between items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div>
                        <div class="font-semibold text-blue-900 dark:text-blue-100">Medium Value Clients</div>
                        <div class="text-sm text-blue-600 dark:text-blue-400">Between 1x-2x average commission</div>
                    </div>
                    <div class="text-2xl font-bold text-blue-900 dark:text-blue-100">
                        <?php echo e(number_format($data['client_segments']['medium_value'])); ?>

                    </div>
                </div>
                
                <div class="flex justify-between items-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div>
                        <div class="font-semibold text-gray-900 dark:text-gray-100">Low Value Clients</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Below average commission</div>
                    </div>
                    <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        <?php echo e(number_format($data['client_segments']['low_value'])); ?>

                    </div>
                </div>
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>

        <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
             <?php $__env->slot('heading', null, []); ?> Retention Analysis <?php $__env->endSlot(); ?>
            
            <div class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <div class="text-blue-600 dark:text-blue-400 text-sm font-medium">Previous Period</div>
                        <div class="text-xl font-bold text-blue-900 dark:text-blue-100">
                            <?php echo e(number_format($data['retention_analysis']['previous_period_clients'])); ?>

                        </div>
                        <div class="text-xs text-blue-600 dark:text-blue-400">clients</div>
                    </div>
                    
                    <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <div class="text-green-600 dark:text-green-400 text-sm font-medium">Current Period</div>
                        <div class="text-xl font-bold text-green-900 dark:text-green-100">
                            <?php echo e(number_format($data['retention_analysis']['current_period_clients'])); ?>

                        </div>
                        <div class="text-xs text-green-600 dark:text-green-400">clients</div>
                    </div>
                </div>
                
                <div class="space-y-3">
                    <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <span class="text-sm font-medium">Retained Clients</span>
                        <span class="text-sm font-semibold text-green-600">
                            <?php echo e(number_format($data['retention_analysis']['retained_clients'])); ?>

                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <span class="text-sm font-medium">New Clients</span>
                        <span class="text-sm font-semibold text-blue-600">
                            <?php echo e(number_format($data['retention_analysis']['new_clients'])); ?>

                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <span class="text-sm font-medium">Lost Clients</span>
                        <span class="text-sm font-semibold text-red-600">
                            <?php echo e(number_format($data['retention_analysis']['lost_clients'])); ?>

                        </span>
                    </div>
                </div>
                
                <div class="grid grid-cols-2 gap-4 mt-4">
                    <div class="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <div class="text-green-600 dark:text-green-400 text-sm font-medium">Retention Rate</div>
                        <div class="text-lg font-bold text-green-900 dark:text-green-100">
                            <?php echo e(number_format($data['retention_analysis']['retention_rate'], 1)); ?>%
                        </div>
                    </div>
                    
                    <div class="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <div class="text-blue-600 dark:text-blue-400 text-sm font-medium">Growth Rate</div>
                        <div class="text-lg font-bold <?php echo e($data['retention_analysis']['growth_rate'] >= 0 ? 'text-green-900 dark:text-green-100' : 'text-red-900 dark:text-red-100'); ?>">
                            <?php echo e(number_format($data['retention_analysis']['growth_rate'], 1)); ?>%
                        </div>
                    </div>
                </div>
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
    </div>

    <!-- Key Insights -->
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('heading', null, []); ?> Key Insights & Recommendations <?php $__env->endSlot(); ?>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
                <h4 class="font-semibold text-gray-900 dark:text-gray-100">Performance Highlights</h4>
                
                <?php
                    $topClient = $data['top_clients']->first();
                    $retentionRate = $data['retention_analysis']['retention_rate'];
                    $highValueClients = $data['client_segments']['high_value'];
                    $totalClients = $data['summary']['total_clients'];
                ?>
                
                <div class="space-y-3">
                    <?php if($topClient): ?>
                        <div class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                            <div class="text-green-800 dark:text-green-200 font-medium">Top Performer</div>
                            <div class="text-green-600 dark:text-green-400 text-sm mt-1">
                                <?php echo e($topClient->client->name ?? 'Unknown Client'); ?> generated 
                                <?php echo e(number_format($topClient->total_commission, 2)); ?> TRY in commission
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <div class="text-blue-800 dark:text-blue-200 font-medium">Client Retention</div>
                        <div class="text-blue-600 dark:text-blue-400 text-sm mt-1">
                            <?php echo e(number_format($retentionRate, 1)); ?>% retention rate 
                            <?php echo e($retentionRate >= 80 ? '(Excellent)' : ($retentionRate >= 60 ? '(Good)' : '(Needs Improvement)')); ?>

                        </div>
                    </div>
                    
                    <div class="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                        <div class="text-purple-800 dark:text-purple-200 font-medium">High Value Segment</div>
                        <div class="text-purple-600 dark:text-purple-400 text-sm mt-1">
                            <?php echo e(number_format($highValueClients)); ?> high-value clients 
                            (<?php echo e($totalClients > 0 ? number_format(($highValueClients / $totalClients) * 100, 1) : '0.0'); ?>% of total)
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="space-y-4">
                <h4 class="font-semibold text-gray-900 dark:text-gray-100">Recommendations</h4>
                
                <div class="space-y-3">
                    <?php if($retentionRate < 70): ?>
                        <div class="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                            <div class="text-yellow-800 dark:text-yellow-200 font-medium">Improve Retention</div>
                            <div class="text-yellow-600 dark:text-yellow-400 text-sm mt-1">
                                Focus on client engagement and loyalty programs to improve retention rate.
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($data['client_segments']['low_value'] > $data['client_segments']['high_value'] * 3): ?>
                        <div class="p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                            <div class="text-orange-800 dark:text-orange-200 font-medium">Value Optimization</div>
                            <div class="text-orange-600 dark:text-orange-400 text-sm mt-1">
                                Consider strategies to move low-value clients to higher value segments.
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <div class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <div class="text-green-800 dark:text-green-200 font-medium">VIP Program</div>
                        <div class="text-green-600 dark:text-green-400 text-sm mt-1">
                            Create exclusive benefits for top <?php echo e(min(20, $highValueClients)); ?> clients to increase loyalty.
                        </div>
                    </div>
                    
                    <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <div class="text-blue-800 dark:text-blue-200 font-medium">Cross-selling</div>
                        <div class="text-blue-600 dark:text-blue-400 text-sm mt-1">
                            Identify opportunities to offer additional services to existing clients.
                        </div>
                    </div>
                </div>
            </div>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
</div>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\filament\company\reports\client-profitability.blade.php ENDPATH**/ ?>