<?php
    $isContained = $isContained();
    $statePath = $getStatePath();
    $previousAction = $getAction('previous');
    $nextAction = $getAction('next');
    $currentStepDescription = $getCurrentStepDescription();
    $areStepTabsHidden = $areStepTabsHidden();
?>

<div
    wire:ignore.self
    x-cloak
    x-data="{
        step: null,

        nextStep: function () {
            let nextStepIndex = this.getStepIndex(this.step) + 1

            if (nextStepIndex >= this.getSteps().length) {
                return
            }

            this.step = this.getSteps()[nextStepIndex]

            this.autofocusFields()
            this.scroll()
        },

        previousStep: function () {
            let previousStepIndex = this.getStepIndex(this.step) - 1

            if (previousStepIndex < 0) {
                return
            }

            this.step = this.getSteps()[previousStepIndex]

            this.autofocusFields()
            this.scroll()
        },

        scroll: function () {
            this.$nextTick(() => {
                this.$refs.header.children[
                    this.getStepIndex(this.step)
                ].scrollIntoView({ behavior: 'smooth', block: 'start' })
            })
        },

        autofocusFields: function () {
            $nextTick(() =>
                this.$refs[`step-${this.step}`]
                    .querySelector('[autofocus]')
                    ?.focus(),
            )
        },

        getStepIndex: function (step) {
            let index = this.getSteps().findIndex(
                (indexedStep) => indexedStep === step,
            )

            if (index === -1) {
                return 0
            }

            return index
        },

        getSteps: function () {
            return JSON.parse(this.$refs.stepsData.value)
        },

        isFirstStep: function () {
            return this.getStepIndex(this.step) <= 0
        },

        isLastStep: function () {
            return this.getStepIndex(this.step) + 1 >= this.getSteps().length
        },

        isStepAccessible: function (stepId) {
            return (
                <?php echo \Illuminate\Support\Js::from($isSkippable())->toHtml() ?> || this.getStepIndex(this.step) > this.getStepIndex(stepId)
            )
        },

        updateQueryString: function () {
            if (! <?php echo \Illuminate\Support\Js::from($isStepPersistedInQueryString())->toHtml() ?>) {
                return
            }

            const url = new URL(window.location.href)
            url.searchParams.set(<?php echo \Illuminate\Support\Js::from($getStepQueryStringKey())->toHtml() ?>, this.step)

            history.pushState(null, document.title, url.toString())
        },
    }"
    x-init="
        $watch('step', () => updateQueryString())

        step = getSteps().at(<?php echo e($getStartStep() - 1); ?>)

        autofocusFields()
    "
    x-on:next-wizard-step.window="if ($event.detail.statePath === '<?php echo e($statePath); ?>') nextStep()"
    <?php echo e($attributes
            ->merge([
                'id' => $getId(),
            ], escape: false)
            ->merge($getExtraAttributes(), escape: false)
            ->merge($getExtraAlpineAttributes(), escape: false)
            ->class([
                'fi-fo-wizard',
                'fi-contained rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10' => $isContained,
            ])); ?>

>
    <input
        type="hidden"
        value="<?php echo e(collect($getChildComponentContainer()->getComponents())
                ->filter(static fn (\Filament\Forms\Components\Wizard\Step $step): bool => $step->isVisible())
                ->map(static fn (\Filament\Forms\Components\Wizard\Step $step) => $step->getId())
                ->values()
                ->toJson()); ?>"
        x-ref="stepsData"
    />

    <?php $__currentLoopData = $getChildComponentContainer()->getComponents(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $step): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div
            x-ref="step-<?php echo e($step->getId()); ?>"
            wire:key="<?php echo e($this->getId()); ?>.<?php echo e($statePath); ?>.<?php echo e($step->getId()); ?>.step"
            x-bind:class="{ 'hidden': step !== '<?php echo e($step->getId()); ?>' }"
        >
            <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => ['id' => 'wizard-step-<?php echo e($step->getId()); ?>','icon' => $step->getIcon()]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('wizard-step-{{ $step->getId() }}'),'icon' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($step->getIcon())]); ?>
                <?php if(!$step->isLabelHidden()): ?>
                     <?php $__env->slot('heading', null, []); ?> 
                        <?php echo e($step->getLabel()); ?>

                     <?php $__env->endSlot(); ?>
                <?php endif; ?>

                <?php if(filled($description = $step->getDescription())): ?>
                     <?php $__env->slot('description', null, []); ?> 
                        <?php echo e($description); ?>

                     <?php $__env->endSlot(); ?>
                <?php endif; ?>

                <?php echo e($step->getChildComponentContainer()); ?>


                <footer
                    class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                        'fi-section-footer py-6',
                    ]); ?>"
                >
                    <div
                        class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                            'flex items-center justify-between gap-x-3',
                        ]); ?>"
                    >
                        <span
                            x-cloak
                            <?php if(! $previousAction->isDisabled()): ?>
                                x-on:click="previousStep"
                            <?php endif; ?>
                            x-show="! isFirstStep()"
                        >
                            <?php echo e($previousAction); ?>

                        </span>

                        <span x-show="isFirstStep()">
                            <?php echo e($getCancelAction()); ?>

                        </span>

                        <span
                            x-cloak
                            <?php if(! $nextAction->isDisabled()): ?>
                                x-on:click="
                                    $wire.dispatchFormEvent(
                                        'wizard::nextStep',
                                        '<?php echo e($statePath); ?>',
                                        getStepIndex(step),
                                    )
                                "
                            <?php endif; ?>
                            x-bind:class="{ 'hidden': isLastStep(), 'block': ! isLastStep() }"
                        >
                            <?php echo e($nextAction); ?>

                        </span>

                        <span
                            x-bind:class="{ 'hidden': ! isLastStep(), 'block': isLastStep() }"
                        >
                            <?php echo e($getSubmitAction()); ?>

                        </span>
                    </div>
                </footer>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\filament\forms\components\linear-wizard.blade.php ENDPATH**/ ?>