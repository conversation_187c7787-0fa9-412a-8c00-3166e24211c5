<div class="space-y-6">
    <!-- Report Header -->
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('heading', null, []); ?> 
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-currency-dollar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                    Profit & Loss Statement
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                    <?php echo e(\Carbon\Carbon::parse($data['period']['start_date'])->format('M d, Y')); ?> - 
                    <?php echo e(\Carbon\Carbon::parse($data['period']['end_date'])->format('M d, Y')); ?>

                    (<?php echo e($data['period']['days']); ?> days)
                </div>
            </div>
         <?php $__env->endSlot(); ?>
        
        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg text-center">
                <div class="text-green-600 dark:text-green-400 text-sm font-medium">Total Revenue</div>
                <div class="text-2xl font-bold text-green-900 dark:text-green-100">
                    <?php echo e(number_format($data['revenue']['total'], 2)); ?> TRY
                </div>
                <div class="text-xs text-green-600 dark:text-green-400 mt-1">
                    <?php echo e(number_format($data['daily_average']['revenue'], 2)); ?> TRY/day
                </div>
            </div>
            
            <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg text-center">
                <div class="text-red-600 dark:text-red-400 text-sm font-medium">Total Expenses</div>
                <div class="text-2xl font-bold text-red-900 dark:text-red-100">
                    <?php echo e(number_format($data['expenses']['total'], 2)); ?> TRY
                </div>
                <div class="text-xs text-red-600 dark:text-red-400 mt-1">
                    <?php echo e(number_format($data['daily_average']['expenses'], 2)); ?> TRY/day
                </div>
            </div>
            
            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg text-center">
                <div class="text-blue-600 dark:text-blue-400 text-sm font-medium">Net Income</div>
                <div class="text-2xl font-bold <?php echo e($data['net_income'] >= 0 ? 'text-green-900 dark:text-green-100' : 'text-red-900 dark:text-red-100'); ?>">
                    <?php echo e(number_format($data['net_income'], 2)); ?> TRY
                </div>
                <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">
                    <?php echo e(number_format($data['profit_margin'], 1)); ?>% margin
                </div>
            </div>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>

    <!-- Detailed P&L Statement -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Revenue Breakdown -->
        <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
             <?php $__env->slot('heading', null, []); ?> Revenue Breakdown <?php $__env->endSlot(); ?>
            
            <div class="space-y-4">
                <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                    <span class="font-medium">Commission Revenue</span>
                    <span class="font-semibold text-green-600">
                        <?php echo e(number_format($data['revenue']['commission_revenue'], 2)); ?> TRY
                    </span>
                </div>
                
                <?php if($data['revenue']['other_revenue'] > 0): ?>
                    <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                        <span class="font-medium">Other Revenue</span>
                        <span class="font-semibold text-green-600">
                            <?php echo e(number_format($data['revenue']['other_revenue'], 2)); ?> TRY
                        </span>
                    </div>
                <?php endif; ?>
                
                <div class="flex justify-between items-center py-3 bg-green-50 dark:bg-green-900/20 px-4 rounded-lg">
                    <span class="font-bold text-green-900 dark:text-green-100">Total Revenue</span>
                    <span class="font-bold text-green-900 dark:text-green-100">
                        <?php echo e(number_format($data['revenue']['total'], 2)); ?> TRY
                    </span>
                </div>
                
                <!-- Revenue by Transaction Type -->
                <?php if(count($data['revenue']['by_type']) > 0): ?>
                    <div class="mt-6">
                        <h4 class="font-semibold text-gray-900 dark:text-gray-100 mb-3">Revenue by Transaction Type</h4>
                        <div class="space-y-2">
                            <?php $__currentLoopData = $data['revenue']['by_type']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $amount): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex justify-between items-center py-1">
                                    <span class="text-sm capitalize"><?php echo e(str_replace('_', ' ', $type)); ?></span>
                                    <span class="text-sm font-medium"><?php echo e(number_format($amount, 2)); ?> TRY</span>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>

        <!-- Expense Breakdown -->
        <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
             <?php $__env->slot('heading', null, []); ?> Expense Breakdown <?php $__env->endSlot(); ?>
            
            <div class="space-y-4">
                <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                    <span class="font-medium">Operational Expenses</span>
                    <span class="font-semibold text-red-600">
                        <?php echo e(number_format($data['expenses']['operational_expenses'], 2)); ?> TRY
                    </span>
                </div>
                
                <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                    <span class="font-medium">Delivery Costs</span>
                    <span class="font-semibold text-red-600">
                        <?php echo e(number_format($data['expenses']['delivery_costs'], 2)); ?> TRY
                    </span>
                </div>
                
                <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                    <span class="font-medium">Administrative Expenses</span>
                    <span class="font-semibold text-red-600">
                        <?php echo e(number_format($data['expenses']['administrative_expenses'], 2)); ?> TRY
                    </span>
                </div>
                
                <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                    <span class="font-medium">Other Expenses</span>
                    <span class="font-semibold text-red-600">
                        <?php echo e(number_format($data['expenses']['other_expenses'], 2)); ?> TRY
                    </span>
                </div>
                
                <div class="flex justify-between items-center py-3 bg-red-50 dark:bg-red-900/20 px-4 rounded-lg">
                    <span class="font-bold text-red-900 dark:text-red-100">Total Expenses</span>
                    <span class="font-bold text-red-900 dark:text-red-100">
                        <?php echo e(number_format($data['expenses']['total'], 2)); ?> TRY
                    </span>
                </div>
                
                <?php if($data['expenses']['total'] == 0): ?>
                    <div class="text-center py-4 text-gray-500 text-sm">
                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-information-circle'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5 mx-auto mb-2']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                        No expense tracking implemented yet.<br>
                        Consider adding expense categories for complete P&L analysis.
                    </div>
                <?php endif; ?>
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
    </div>

    <!-- Performance Metrics -->
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('heading', null, []); ?> Performance Metrics <?php $__env->endSlot(); ?>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div class="text-gray-600 dark:text-gray-400 text-sm font-medium">Profit Margin</div>
                <div class="text-xl font-bold <?php echo e($data['profit_margin'] >= 0 ? 'text-green-600' : 'text-red-600'); ?>">
                    <?php echo e(number_format($data['profit_margin'], 2)); ?>%
                </div>
            </div>
            
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div class="text-gray-600 dark:text-gray-400 text-sm font-medium">Daily Avg Revenue</div>
                <div class="text-xl font-bold text-green-600">
                    <?php echo e(number_format($data['daily_average']['revenue'], 2)); ?> TRY
                </div>
            </div>
            
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div class="text-gray-600 dark:text-gray-400 text-sm font-medium">Daily Avg Expenses</div>
                <div class="text-xl font-bold text-red-600">
                    <?php echo e(number_format($data['daily_average']['expenses'], 2)); ?> TRY
                </div>
            </div>
            
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div class="text-gray-600 dark:text-gray-400 text-sm font-medium">Daily Avg Profit</div>
                <div class="text-xl font-bold <?php echo e($data['daily_average']['net_income'] >= 0 ? 'text-green-600' : 'text-red-600'); ?>">
                    <?php echo e(number_format($data['daily_average']['net_income'], 2)); ?> TRY
                </div>
            </div>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>

    <!-- Summary -->
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('heading', null, []); ?> Summary <?php $__env->endSlot(); ?>
        
        <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
            <div class="text-center">
                <div class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                    Financial Performance Summary
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    For the period <?php echo e(\Carbon\Carbon::parse($data['period']['start_date'])->format('M d, Y')); ?> - 
                    <?php echo e(\Carbon\Carbon::parse($data['period']['end_date'])->format('M d, Y')); ?>

                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                    <div>
                        <div class="text-2xl font-bold text-green-600">
                            <?php echo e(number_format($data['revenue']['total'], 0)); ?> TRY
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Total Revenue</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-red-600">
                            <?php echo e(number_format($data['expenses']['total'], 0)); ?> TRY
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Total Expenses</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold <?php echo e($data['net_income'] >= 0 ? 'text-green-600' : 'text-red-600'); ?>">
                            <?php echo e(number_format($data['net_income'], 0)); ?> TRY
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Net Income</div>
                    </div>
                </div>
            </div>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
</div>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\filament\company\reports\profit-loss.blade.php ENDPATH**/ ?>