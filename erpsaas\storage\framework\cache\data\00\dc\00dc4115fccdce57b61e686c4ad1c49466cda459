1756292109O:49:"App\Transformers\IncomeStatementReportTransformer":4:{s:9:" * report";O:17:"App\DTO\ReportDTO":10:{s:10:"categories";a:3:{s:7:"Revenue";O:26:"App\DTO\AccountCategoryDTO":3:{s:8:"accounts";a:0:{}s:5:"types";N;s:7:"summary";O:25:"App\DTO\AccountBalanceDTO":5:{s:15:"startingBalance";N;s:12:"debitBalance";N;s:13:"creditBalance";N;s:11:"netMovement";s:6:"A$0.00";s:13:"endingBalance";N;}}s:18:"Cost of Goods Sold";O:26:"App\DTO\AccountCategoryDTO":3:{s:8:"accounts";a:0:{}s:5:"types";N;s:7:"summary";O:25:"App\DTO\AccountBalanceDTO":5:{s:15:"startingBalance";N;s:12:"debitBalance";N;s:13:"creditBalance";N;s:11:"netMovement";s:6:"A$0.00";s:13:"endingBalance";N;}}s:8:"Expenses";O:26:"App\DTO\AccountCategoryDTO":3:{s:8:"accounts";a:0:{}s:5:"types";N;s:7:"summary";O:25:"App\DTO\AccountBalanceDTO":5:{s:15:"startingBalance";N;s:12:"debitBalance";N;s:13:"creditBalance";N;s:11:"netMovement";s:6:"A$0.00";s:13:"endingBalance";N;}}}s:12:"overallTotal";O:25:"App\DTO\AccountBalanceDTO":5:{s:15:"startingBalance";N;s:12:"debitBalance";N;s:13:"creditBalance";N;s:11:"netMovement";s:6:"A$0.00";s:13:"endingBalance";N;}s:12:"agingSummary";N;s:18:"entityBalanceTotal";N;s:21:"overallPaymentMetrics";N;s:6:"fields";a:2:{i:0;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:0;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";E:37:"Filament\Support\Enums\Alignment:Left";s:8:" * label";s:8:"ACCOUNTS";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:12:"account_name";}i:1;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:0;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";E:38:"Filament\Support\Enums\Alignment:Right";s:8:" * label";s:26:"Jan 1, 2025 - Aug 27, 2025";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:12:"net_movement";}}s:10:"reportType";N;s:8:"overview";N;s:9:"startDate";O:25:"Illuminate\Support\Carbon":3:{s:4:"date";s:26:"2025-01-01 00:00:00.000000";s:13:"timezone_type";i:3;s:8:"timezone";s:3:"UTC";}s:7:"endDate";O:25:"Illuminate\Support\Carbon":3:{s:4:"date";s:26:"2025-08-27 23:59:59.000000";s:13:"timezone_type";i:3;s:8:"timezone";s:3:"UTC";}}s:15:" * totalRevenue";s:6:"A$0.00";s:12:" * totalCogs";s:6:"A$0.00";s:16:" * totalExpenses";s:6:"A$0.00";}