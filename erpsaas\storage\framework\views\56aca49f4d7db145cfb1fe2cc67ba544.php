<span>Are you sure you want to delete your <?php echo e($institution->name); ?> connection? Deleting this bank connection will remove the following connected accounts:</span>
<ul class="list-disc list-inside p-4">
    <?php $__currentLoopData = $institution->connectedBankAccounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $connectedBankAccount): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li><?php echo e($connectedBankAccount->name); ?></li>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</ul>
<span>Deleting this bank connection will stop the import of transactions for all accounts associated with this bank. Existing transactions will remain unchanged.</span>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\actions\delete-bank-connection-modal.blade.php ENDPATH**/ ?>