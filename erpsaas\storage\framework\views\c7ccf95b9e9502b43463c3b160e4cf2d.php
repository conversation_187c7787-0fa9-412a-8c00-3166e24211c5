<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'item',
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'item',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<?php if(isset($item['panelId'])): ?>
    
    <?php if (isset($component)) { $__componentOriginal4e2351c219bf8efe995b383efaa9f871 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4e2351c219bf8efe995b383efaa9f871 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.panel-shift-dropdown.toggle','data' => ['label' => $item['label'],'icon' => $item['icon'],'panelId' => $item['panelId']]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('panel-shift-dropdown.toggle'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($item['label']),'icon' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($item['icon']),'panel-id' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($item['panelId'])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4e2351c219bf8efe995b383efaa9f871)): ?>
<?php $attributes = $__attributesOriginal4e2351c219bf8efe995b383efaa9f871; ?>
<?php unset($__attributesOriginal4e2351c219bf8efe995b383efaa9f871); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4e2351c219bf8efe995b383efaa9f871)): ?>
<?php $component = $__componentOriginal4e2351c219bf8efe995b383efaa9f871; ?>
<?php unset($__componentOriginal4e2351c219bf8efe995b383efaa9f871); ?>
<?php endif; ?>
<?php elseif(!empty($item['items'])): ?>
    
    <?php $__currentLoopData = $item['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $nestedItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if (isset($component)) { $__componentOriginal6b5781ad5a341ed884cacb9ebab6310c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6b5781ad5a341ed884cacb9ebab6310c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.panel-shift-dropdown.content-handler','data' => ['item' => $nestedItem]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('panel-shift-dropdown.content-handler'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['item' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($nestedItem)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6b5781ad5a341ed884cacb9ebab6310c)): ?>
<?php $attributes = $__attributesOriginal6b5781ad5a341ed884cacb9ebab6310c; ?>
<?php unset($__attributesOriginal6b5781ad5a341ed884cacb9ebab6310c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6b5781ad5a341ed884cacb9ebab6310c)): ?>
<?php $component = $__componentOriginal6b5781ad5a341ed884cacb9ebab6310c; ?>
<?php unset($__componentOriginal6b5781ad5a341ed884cacb9ebab6310c); ?>
<?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php elseif(isset($item['url'])): ?>
    
    <?php if (isset($component)) { $__componentOriginal2677d6ad3067c4282b32a41c21dedc6d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.panel-shift-dropdown.item','data' => ['url' => $item['url'],'label' => $item['label'],'icon' => $item['icon']]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('panel-shift-dropdown.item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['url' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($item['url']),'label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($item['label']),'icon' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($item['icon'])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d)): ?>
<?php $attributes = $__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d; ?>
<?php unset($__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2677d6ad3067c4282b32a41c21dedc6d)): ?>
<?php $component = $__componentOriginal2677d6ad3067c4282b32a41c21dedc6d; ?>
<?php unset($__componentOriginal2677d6ad3067c4282b32a41c21dedc6d); ?>
<?php endif; ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\panel-shift-dropdown\content-handler.blade.php ENDPATH**/ ?>