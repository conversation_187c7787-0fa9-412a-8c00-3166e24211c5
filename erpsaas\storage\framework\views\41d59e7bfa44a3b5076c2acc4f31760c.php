<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['disabled' => false, 'id' => null]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['disabled' => false, 'id' => null]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<input
    <?php echo e($disabled ? 'disabled' : ''); ?>

    <?php echo $attributes->class([
        'filament-companies-input block w-full transition duration-75 rounded-lg shadow-sm outline-none focus:ring-1 focus:ring-inset disabled:opacity-70',
        'dark:bg-gray-700 dark:text-white' => config('forms.dark_mode'),
        'border-gray-300 focus:border-primary-500 focus:ring-primary-500' => ! $errors->has($id),
        'dark:border-gray-600 dark:focus:border-primary-500' => ! $errors->has($id) && config('forms.dark_mode'),
        'border-danger-600 ring-danger-600 focus:border-danger-500 focus:ring-danger-500' => $errors->has($id),
        'dark:border-danger-400 dark:ring-danger-400 dark:focus:border-danger-400 dark:focus:ring-danger-400' => $errors->has($id) && config('forms.dark_mode'),
    ]); ?>

/>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\vendor\andrewdwallo\filament-companies\resources\views\components\input.blade.php ENDPATH**/ ?>