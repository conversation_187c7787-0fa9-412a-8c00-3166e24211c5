<?php $__env->startSection('content'); ?>
    <div class="header">
        <div class="title"><?php echo e($report->getTitle()); ?></div>
        <div class="company-name"><?php echo e($company->name); ?></div>
        <?php if($startDate && $endDate): ?>
            <div class="date-range">Date Range: <?php echo e($startDate); ?> to <?php echo e($endDate); ?></div>
        <?php else: ?>
            <div class="date-range">As of <?php echo e($endDate); ?></div>
        <?php endif; ?>
    </div>
    <table class="table-class">
        <colgroup>
            <col span="1" style="width: 65%;">
            <col span="1" style="width: 35%;">
        </colgroup>
        <thead class="table-head">
        <tr>
            <?php $__currentLoopData = $report->getSummaryHeaders(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $header): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <th class="<?php echo e($report->getAlignmentClass($index)); ?>">
                    <?php echo e($header); ?>

                </th>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tr>
        </thead>
        <?php $__currentLoopData = $report->getSummaryCategories(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tbody>
            <tr class="category-header-row">
                <?php $__currentLoopData = $category->header; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $header): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <td class="<?php echo e($report->getAlignmentClass($index)); ?>">
                        <?php echo e($header); ?>

                    </td>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tr>

            <!-- Category Types -->
            <?php $__currentLoopData = $category->types ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <!-- Type Summary -->
                <tr>
                    <?php $__currentLoopData = $type->summary; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $cell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <td class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                            $report->getAlignmentClass($index),
                        ]); ?>"
                        >
                            <?php echo e($cell); ?>

                        </td>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            <tr class="category-summary-row">
                <?php $__currentLoopData = $category->summary; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $cell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <td class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                        $report->getAlignmentClass($index),
                        'underline-bold' => $loop->last && $report->getTitle() === 'Cash Flow Statement',
                    ]); ?>"
                    >
                        <?php echo e($cell); ?>

                    </td>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tr>

            <?php if($category->summary['account_name'] === 'Cost of Goods Sold'): ?>
                <tr class="category-header-row">
                    <?php $__currentLoopData = $report->getGrossProfit(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $grossProfitIndex => $grossProfitCell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <td class="<?php echo e($report->getAlignmentClass($grossProfitIndex)); ?>">
                            <?php echo e($grossProfitCell); ?>

                        </td>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tr>
            <?php endif; ?>
            </tbody>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <tfoot>
        <tr class="table-footer-row">
            <?php $__currentLoopData = $report->getOverallTotals(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $total): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <td class="<?php echo e($report->getAlignmentClass($index)); ?>">
                    <?php echo e($total); ?>

                </td>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tr>
        </tfoot>
    </table>

    <!-- Second Overview Table -->
    <?php if(method_exists($report, 'getSummaryOverviewHeaders') && filled($report->getSummaryOverviewHeaders())): ?>
        <table class="table-class" style="margin-top: 40px;">
            <colgroup>
                <col span="1" style="width: 65%;">
                <col span="1" style="width: 35%;">
            </colgroup>
            <thead class="table-head">
            <tr>
                <?php $__currentLoopData = $report->getOverviewHeaders(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $header): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <th class="<?php echo e($report->getAlignmentClass($index)); ?>">
                        <?php echo e($header); ?>

                    </th>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tr>
            </thead>
            <!-- Overview Content -->
            <?php $__currentLoopData = $report->getSummaryOverview(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $overviewCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tbody>
                <!-- Summary Row -->
                <tr class="category-header-row">
                    <?php $__currentLoopData = $overviewCategory->summary; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $summaryCell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <td class="<?php echo e($report->getAlignmentClass($index)); ?>">
                            <?php echo e($summaryCell); ?>

                        </td>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tr>

                <?php if($overviewCategory->summary['account_name'] === 'Starting Balance'): ?>
                    <?php $__currentLoopData = $report->getSummaryOverviewAlignedWithColumns(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $summaryRow): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <?php $__currentLoopData = $summaryRow; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $summaryCell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                                'cell',
                                $report->getAlignmentClass($index),
                                'font-bold' => $loop->parent->last,
                                'underline-thin' => $loop->parent->remaining === 1 && $index === 'net_movement',
                                'underline-bold' => $loop->parent->last && $index === 'net_movement',
                            ]); ?>"
                                >
                                    <?php echo e($summaryCell); ?>

                                </td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
                </tbody>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </table>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('components.company.reports.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\company\reports\summary-report-pdf.blade.php ENDPATH**/ ?>