<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'alignmentClass',
    'indent' => false,
    'bold' => false,
    'underlineThin' => false,
    'underlineBold' => false,
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'alignmentClass',
    'indent' => false,
    'bold' => false,
    'underlineThin' => false,
    'underlineBold' => false,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<td
    class="<?php echo \Illuminate\Support\Arr::toCssClasses([
        $alignmentClass,
        'last-of-type:pe-1 sm:last-of-type:pe-3',
        'ps-4 sm:first-of-type:ps-7' => $indent,
        'p-0 first-of-type:ps-1 sm:first-of-type:ps-3' => ! $indent,
    ]); ?>"
>
    <div
        class="<?php echo \Illuminate\Support\Arr::toCssClasses([
            'px-3 py-4 text-sm leading-6 text-gray-950 dark:text-white',
            'font-semibold' => $bold,
            'border-b border-gray-700 dark:border-white/10' => $underlineThin,
            'border-b-[1.5px] border-gray-800 dark:border-white/5' => $underlineBold,
        ]); ?>"
    >
        <?php echo e($slot); ?>

    </div>
</td>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\company\tables\cell.blade.php ENDPATH**/ ?>