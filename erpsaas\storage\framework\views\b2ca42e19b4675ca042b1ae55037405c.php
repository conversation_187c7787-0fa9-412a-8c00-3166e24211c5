<?php
    $user = filament()->auth()->user();
    $items = filament()->getUserMenuItems();
    $logoutItem = $items['logout'] ?? null;
    $currentTenant = filament()->getTenant();
    $currentTenantName = $currentTenant ? filament()->getTenantName($currentTenant) : null;

    $navigation = $component->getNavigation();
    $hasDisplayAndAccessibility = $component->hasDisplayAndAccessibility();
    $hasCompanySettings = $component->hasCompanySettings();
    $hasLogoutItem = $component->hasLogoutItem();
    $panels = $component->getNavigationAsHierarchyArray();
?>

<div x-data="panelShiftDropdown" x-on:click.outside="closeDropdown">
    <div x-on:click="toggleDropdown" class="flex cursor-pointer">
        <button
            type="button"
            class="flex items-center justify-center gap-x-3 rounded-lg p-2 text-sm font-medium outline-none transition duration-75 hover:bg-gray-100 focus-visible:bg-gray-100 dark:hover:bg-white/5 dark:focus-visible:bg-white/5"
        >
            <?php if($currentTenant): ?>
                <?php if (isset($component)) { $__componentOriginale6928a7cc4d74387315a46a9adb2eaaa = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale6928a7cc4d74387315a46a9adb2eaaa = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.avatar.tenant','data' => ['tenant' => $currentTenant,'class' => 'shrink-0']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::avatar.tenant'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['tenant' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentTenant),'class' => 'shrink-0']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale6928a7cc4d74387315a46a9adb2eaaa)): ?>
<?php $attributes = $__attributesOriginale6928a7cc4d74387315a46a9adb2eaaa; ?>
<?php unset($__attributesOriginale6928a7cc4d74387315a46a9adb2eaaa); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale6928a7cc4d74387315a46a9adb2eaaa)): ?>
<?php $component = $__componentOriginale6928a7cc4d74387315a46a9adb2eaaa; ?>
<?php unset($__componentOriginale6928a7cc4d74387315a46a9adb2eaaa); ?>
<?php endif; ?>
            <?php else: ?>
                <?php if (isset($component)) { $__componentOriginalceea4679a368984135244eacf4aafeca = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalceea4679a368984135244eacf4aafeca = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.avatar.user','data' => ['user' => $user,'class' => 'shrink-0']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::avatar.user'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['user' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user),'class' => 'shrink-0']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalceea4679a368984135244eacf4aafeca)): ?>
<?php $attributes = $__attributesOriginalceea4679a368984135244eacf4aafeca; ?>
<?php unset($__attributesOriginalceea4679a368984135244eacf4aafeca); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalceea4679a368984135244eacf4aafeca)): ?>
<?php $component = $__componentOriginalceea4679a368984135244eacf4aafeca; ?>
<?php unset($__componentOriginalceea4679a368984135244eacf4aafeca); ?>
<?php endif; ?>
            <?php endif; ?>

            <span class="grid justify-items-start text-start">
                <?php if($currentTenant instanceof \Filament\Models\Contracts\HasCurrentTenantLabel): ?>
                    <span class="text-xs text-gray-500 dark:text-gray-400">
                        <?php echo e($currentTenant->getCurrentTenantLabel()); ?>

                    </span>
                <?php endif; ?>

                <span class="text-gray-950 dark:text-white">
                    <?php echo e($currentTenantName ?? filament()->getUserName($user)); ?>

                </span>
            </span>

            <?php if (isset($component)) { $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.icon','data' => ['icon' => 'heroicon-m-chevron-down','class' => 'h-5 w-5 transition duration-75 text-gray-400 group-hover:text-gray-500 group-focus-visible:text-gray-500 dark:text-gray-500 dark:group-hover:text-gray-400 dark:group-focus-visible:text-gray-400','xBind:class' => '{ \'rotate-180\': open }']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'heroicon-m-chevron-down','class' => 'h-5 w-5 transition duration-75 text-gray-400 group-hover:text-gray-500 group-focus-visible:text-gray-500 dark:text-gray-500 dark:group-hover:text-gray-400 dark:group-focus-visible:text-gray-400','x-bind:class' => '{ \'rotate-180\': open }']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $attributes = $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $component = $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
        </button>
    </div>
    <div
        x-show="open"
        class="flex flex-col transition duration-200 ease-in-out grow shrink top-16 fixed z-10 w-screen max-w-[360px] end-4 sm:end-8 rounded-lg bg-white shadow-lg ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 overflow-hidden"
    >
        <?php $__currentLoopData = $panels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $panelId => $panel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if (isset($component)) { $__componentOriginal72ff75c1aa09b3ec5ec09fee57935edc = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal72ff75c1aa09b3ec5ec09fee57935edc = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.panel-shift-dropdown.panel','data' => ['panelId' => $panelId]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('panel-shift-dropdown.panel'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['panel-id' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($panelId)]); ?>
                <?php if($panelId !== 'main' && isset($panel['label'])): ?>
                    <?php if (isset($component)) { $__componentOriginal90a71fc88214a3ff950835dd1f7ef636 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal90a71fc88214a3ff950835dd1f7ef636 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.panel-shift-dropdown.subpanel-header','data' => ['label' => $panel['label'],'panelId' => $panelId]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('panel-shift-dropdown.subpanel-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($panel['label']),'panel-id' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($panelId)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal90a71fc88214a3ff950835dd1f7ef636)): ?>
<?php $attributes = $__attributesOriginal90a71fc88214a3ff950835dd1f7ef636; ?>
<?php unset($__attributesOriginal90a71fc88214a3ff950835dd1f7ef636); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal90a71fc88214a3ff950835dd1f7ef636)): ?>
<?php $component = $__componentOriginal90a71fc88214a3ff950835dd1f7ef636; ?>
<?php unset($__componentOriginal90a71fc88214a3ff950835dd1f7ef636); ?>
<?php endif; ?>
                <?php endif; ?>
                <?php if($panel['renderItems']): ?>
                    <?php $__currentLoopData = $panel['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if (isset($component)) { $__componentOriginal6b5781ad5a341ed884cacb9ebab6310c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6b5781ad5a341ed884cacb9ebab6310c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.panel-shift-dropdown.content-handler','data' => ['item' => $item]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('panel-shift-dropdown.content-handler'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['item' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($item)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6b5781ad5a341ed884cacb9ebab6310c)): ?>
<?php $attributes = $__attributesOriginal6b5781ad5a341ed884cacb9ebab6310c; ?>
<?php unset($__attributesOriginal6b5781ad5a341ed884cacb9ebab6310c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6b5781ad5a341ed884cacb9ebab6310c)): ?>
<?php $component = $__componentOriginal6b5781ad5a341ed884cacb9ebab6310c; ?>
<?php unset($__componentOriginal6b5781ad5a341ed884cacb9ebab6310c); ?>
<?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
                <?php if($panelId === 'company-settings' && $currentTenant): ?>
                    <?php if (isset($component)) { $__componentOriginal0d2882482fb832987e8226e6637f6d09 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0d2882482fb832987e8226e6637f6d09 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.panel-shift-dropdown.company-settings','data' => ['currentTenant' => $currentTenant,'icon' => 'heroicon-m-building-office-2']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('panel-shift-dropdown.company-settings'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['current-tenant' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentTenant),'icon' => 'heroicon-m-building-office-2']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0d2882482fb832987e8226e6637f6d09)): ?>
<?php $attributes = $__attributesOriginal0d2882482fb832987e8226e6637f6d09; ?>
<?php unset($__attributesOriginal0d2882482fb832987e8226e6637f6d09); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0d2882482fb832987e8226e6637f6d09)): ?>
<?php $component = $__componentOriginal0d2882482fb832987e8226e6637f6d09; ?>
<?php unset($__componentOriginal0d2882482fb832987e8226e6637f6d09); ?>
<?php endif; ?>
                <?php endif; ?>
                <?php if($panelId === 'company-switcher' && $currentTenant): ?>
                    <?php if (isset($component)) { $__componentOriginal0ec644e4f4e55820a4431c1ae95e5001 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0ec644e4f4e55820a4431c1ae95e5001 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.panel-shift-dropdown.company-switcher','data' => ['currentTenant' => $currentTenant,'icon' => 'heroicon-m-adjustments-horizontal']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('panel-shift-dropdown.company-switcher'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['current-tenant' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentTenant),'icon' => 'heroicon-m-adjustments-horizontal']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0ec644e4f4e55820a4431c1ae95e5001)): ?>
<?php $attributes = $__attributesOriginal0ec644e4f4e55820a4431c1ae95e5001; ?>
<?php unset($__attributesOriginal0ec644e4f4e55820a4431c1ae95e5001); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0ec644e4f4e55820a4431c1ae95e5001)): ?>
<?php $component = $__componentOriginal0ec644e4f4e55820a4431c1ae95e5001; ?>
<?php unset($__componentOriginal0ec644e4f4e55820a4431c1ae95e5001); ?>
<?php endif; ?>
                <?php endif; ?>
                <?php if($panelId === 'display-and-accessibility'): ?>
                    <?php if (isset($component)) { $__componentOriginal25a72dcc27bcac78b75c9b70a7027526 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal25a72dcc27bcac78b75c9b70a7027526 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.panel-shift-dropdown.display-accessibility','data' => ['icon' => 'heroicon-s-moon']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('panel-shift-dropdown.display-accessibility'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'heroicon-s-moon']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal25a72dcc27bcac78b75c9b70a7027526)): ?>
<?php $attributes = $__attributesOriginal25a72dcc27bcac78b75c9b70a7027526; ?>
<?php unset($__attributesOriginal25a72dcc27bcac78b75c9b70a7027526); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal25a72dcc27bcac78b75c9b70a7027526)): ?>
<?php $component = $__componentOriginal25a72dcc27bcac78b75c9b70a7027526; ?>
<?php unset($__componentOriginal25a72dcc27bcac78b75c9b70a7027526); ?>
<?php endif; ?>
                <?php endif; ?>
                <?php if($panelId === 'main' && $hasLogoutItem): ?>
                    <?php if (isset($component)) { $__componentOriginal2677d6ad3067c4282b32a41c21dedc6d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.panel-shift-dropdown.item','data' => ['tag' => 'form','method' => 'post','action' => $logoutItem?->getUrl() ?? filament()->getLogoutUrl(),'label' => $logoutItem?->getLabel() ?? __('filament-panels::layout.actions.logout.label'),'icon' => $logoutItem?->getIcon() ?? \Filament\Support\Facades\FilamentIcon::resolve('panels::user-menu.logout-button') ?? 'heroicon-m-arrow-left-on-rectangle']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('panel-shift-dropdown.item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['tag' => 'form','method' => 'post','action' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($logoutItem?->getUrl() ?? filament()->getLogoutUrl()),'label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($logoutItem?->getLabel() ?? __('filament-panels::layout.actions.logout.label')),'icon' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($logoutItem?->getIcon() ?? \Filament\Support\Facades\FilamentIcon::resolve('panels::user-menu.logout-button') ?? 'heroicon-m-arrow-left-on-rectangle')]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d)): ?>
<?php $attributes = $__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d; ?>
<?php unset($__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2677d6ad3067c4282b32a41c21dedc6d)): ?>
<?php $component = $__componentOriginal2677d6ad3067c4282b32a41c21dedc6d; ?>
<?php unset($__componentOriginal2677d6ad3067c4282b32a41c21dedc6d); ?>
<?php endif; ?>
                <?php endif; ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal72ff75c1aa09b3ec5ec09fee57935edc)): ?>
<?php $attributes = $__attributesOriginal72ff75c1aa09b3ec5ec09fee57935edc; ?>
<?php unset($__attributesOriginal72ff75c1aa09b3ec5ec09fee57935edc); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal72ff75c1aa09b3ec5ec09fee57935edc)): ?>
<?php $component = $__componentOriginal72ff75c1aa09b3ec5ec09fee57935edc; ?>
<?php unset($__componentOriginal72ff75c1aa09b3ec5ec09fee57935edc); ?>
<?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('panelShiftDropdown', () => ({
            open: false,
            navigationStack: ['main'],
            theme: localStorage.getItem('theme') || '<?php echo e(filament()->getDefaultThemeMode()->value); ?>',
            themeLabels: {
                light: 'Off',
                dark: 'On',
                system: 'System',
            },

            toggleDropdown() {
                this.open = !this.open;
            },

            closeDropdown() {
                this.open = false;
            },

            setActiveMenu(menu) {
                this.transitionPanel(menu, 'forward');
            },

            focusMenuItem(menuItemRef) {
                this.$nextTick(() => {
                    setTimeout(() => {
                        this.$refs[menuItemRef]?.focus();
                    }, 200);
                });
            },

            focusBackButton(backButtonRef) {
                this.$nextTick(() => {
                    setTimeout(() => {
                        this.$refs[backButtonRef]?.focus();
                    }, 200);
                });
            },

            goBack() {
                if (this.open && this.navigationStack.length > 1) {
                    this.transitionPanel(this.navigationStack.at(-2), 'back');
                }
            },

            currentActiveMenu() {
                return this.navigationStack.at(-1);
            },

            transitionPanel(target, direction) {
                const currentPanel = this.$refs[this.currentActiveMenu()];
                const targetPanel = this.$refs[target];

                const translateX = direction === 'forward' ? '-100%' : '100%';
                currentPanel.style.transform = `translateX(${translateX})`;

                setTimeout(() => {
                    currentPanel.classList.add('hide');
                    targetPanel.classList.remove('hide');
                    targetPanel.style.transform = 'translateX(0)';

                    if (direction === 'forward') {
                        this.navigationStack.push(target);
                    } else {
                        this.navigationStack.pop();
                    }
                }, 200);
            },

            setTheme(newTheme) {
                this.theme = newTheme;
            },

            init() {
                this.$watch('theme', (value) => {
                    this.$dispatch('theme-changed', value);
                });

                this.$watch('open', (value) => {
                    if (value) {
                        if (this.navigationStack.length === 1) {
                            const mainPanel = this.$refs.main;
                            mainPanel.classList.remove('hide');
                            mainPanel.style.transform = 'translateX(0)';
                        }
                    } else {
                        if (this.currentActiveMenu() !== 'main') {
                            this.setActiveMenu('main');
                        }
                    }
                });
            },

            getThemeLabel(value) {
                return this.themeLabels[value] || value;
            },
        }));
    });
</script>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\panel-shift-dropdown.blade.php ENDPATH**/ ?>