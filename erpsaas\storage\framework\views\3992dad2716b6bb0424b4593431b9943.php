<table class="w-full min-w-[50rem] divide-y divide-gray-200 dark:divide-white/5">
    <colgroup>
        <col span="1" style="width: 65%;">
        <col span="1" style="width: 35%;">
    </colgroup>
    <?php if (isset($component)) { $__componentOriginal088d9df2e25f0de01ebf6280a5631361 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal088d9df2e25f0de01ebf6280a5631361 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.tables.header','data' => ['headers' => $report->getSummaryHeaders(),'alignmentClass' => [$report, 'getAlignmentClass']]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.tables.header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['headers' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($report->getSummaryHeaders()),'alignment-class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([$report, 'getAlignmentClass'])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal088d9df2e25f0de01ebf6280a5631361)): ?>
<?php $attributes = $__attributesOriginal088d9df2e25f0de01ebf6280a5631361; ?>
<?php unset($__attributesOriginal088d9df2e25f0de01ebf6280a5631361); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal088d9df2e25f0de01ebf6280a5631361)): ?>
<?php $component = $__componentOriginal088d9df2e25f0de01ebf6280a5631361; ?>
<?php unset($__componentOriginal088d9df2e25f0de01ebf6280a5631361); ?>
<?php endif; ?>
    <?php $__currentLoopData = $report->getSummaryCategories(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $accountCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <tbody class="divide-y divide-gray-200 whitespace-nowrap dark:divide-white/5">
        <tr>
            <?php $__currentLoopData = $accountCategory->summary; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $accountCategorySummaryIndex => $accountCategorySummaryCell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if (isset($component)) { $__componentOriginal24b82ed7131e46c2ad55192929ed9db8 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal24b82ed7131e46c2ad55192929ed9db8 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.tables.cell','data' => ['alignmentClass' => $report->getAlignmentClass($accountCategorySummaryIndex)]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.tables.cell'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['alignment-class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($report->getAlignmentClass($accountCategorySummaryIndex))]); ?>
                    <?php echo e($accountCategorySummaryCell); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal24b82ed7131e46c2ad55192929ed9db8)): ?>
<?php $attributes = $__attributesOriginal24b82ed7131e46c2ad55192929ed9db8; ?>
<?php unset($__attributesOriginal24b82ed7131e46c2ad55192929ed9db8); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal24b82ed7131e46c2ad55192929ed9db8)): ?>
<?php $component = $__componentOriginal24b82ed7131e46c2ad55192929ed9db8; ?>
<?php unset($__componentOriginal24b82ed7131e46c2ad55192929ed9db8); ?>
<?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tr>

        <?php if($accountCategory->summary['account_name'] === 'Cost of Goods Sold'): ?>
            <tr class="bg-gray-50 dark:bg-white/5">
                <?php $__currentLoopData = $report->getGrossProfit(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $grossProfitIndex => $grossProfitCell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if (isset($component)) { $__componentOriginal24b82ed7131e46c2ad55192929ed9db8 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal24b82ed7131e46c2ad55192929ed9db8 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.tables.cell','data' => ['alignmentClass' => $report->getAlignmentClass($grossProfitIndex),'bold' => 'true']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.tables.cell'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['alignment-class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($report->getAlignmentClass($grossProfitIndex)),'bold' => 'true']); ?>
                        <?php echo e($grossProfitCell); ?>

                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal24b82ed7131e46c2ad55192929ed9db8)): ?>
<?php $attributes = $__attributesOriginal24b82ed7131e46c2ad55192929ed9db8; ?>
<?php unset($__attributesOriginal24b82ed7131e46c2ad55192929ed9db8); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal24b82ed7131e46c2ad55192929ed9db8)): ?>
<?php $component = $__componentOriginal24b82ed7131e46c2ad55192929ed9db8; ?>
<?php unset($__componentOriginal24b82ed7131e46c2ad55192929ed9db8); ?>
<?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tr>
        <?php endif; ?>
        </tbody>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php if (isset($component)) { $__componentOriginal2eba023e47eb1bdadd50ce574b3e9d81 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2eba023e47eb1bdadd50ce574b3e9d81 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.tables.footer','data' => ['totals' => $report->getSummaryOverallTotals(),'alignmentClass' => [$report, 'getAlignmentClass']]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.tables.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['totals' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($report->getSummaryOverallTotals()),'alignment-class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([$report, 'getAlignmentClass'])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2eba023e47eb1bdadd50ce574b3e9d81)): ?>
<?php $attributes = $__attributesOriginal2eba023e47eb1bdadd50ce574b3e9d81; ?>
<?php unset($__attributesOriginal2eba023e47eb1bdadd50ce574b3e9d81); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2eba023e47eb1bdadd50ce574b3e9d81)): ?>
<?php $component = $__componentOriginal2eba023e47eb1bdadd50ce574b3e9d81; ?>
<?php unset($__componentOriginal2eba023e47eb1bdadd50ce574b3e9d81); ?>
<?php endif; ?>
</table>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\company\tables\reports\income-statement-summary.blade.php ENDPATH**/ ?>