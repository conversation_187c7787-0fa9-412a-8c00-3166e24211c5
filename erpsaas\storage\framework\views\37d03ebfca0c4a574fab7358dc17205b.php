<?php if (isset($component)) { $__componentOriginal21b9196bf26e13953fd5bee32e75c09f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal21b9196bf26e13953fd5bee32e75c09f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.document-template.container','data' => ['class' => 'modern-template-container','preview' => $preview]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.document-template.container'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'modern-template-container','preview' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($preview)]); ?>
    <!-- Colored Header with Logo -->
    <?php if (isset($component)) { $__componentOriginal991b1314be9f8283186b0484b7191723 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal991b1314be9f8283186b0484b7191723 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.document-template.header','data' => ['class' => 'bg-gray-800 h-24']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.document-template.header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'bg-gray-800 h-24']); ?>
        <!-- Logo -->
        <div class="w-2/3">
            <?php if($document->logo && $document->showLogo): ?>
                <?php if (isset($component)) { $__componentOriginal623144f469b1e9f2db1f8e75864429b6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal623144f469b1e9f2db1f8e75864429b6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.document-template.logo','data' => ['class' => 'ml-8','src' => $document->logo]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.document-template.logo'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'ml-8','src' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($document->logo)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal623144f469b1e9f2db1f8e75864429b6)): ?>
<?php $attributes = $__attributesOriginal623144f469b1e9f2db1f8e75864429b6; ?>
<?php unset($__attributesOriginal623144f469b1e9f2db1f8e75864429b6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal623144f469b1e9f2db1f8e75864429b6)): ?>
<?php $component = $__componentOriginal623144f469b1e9f2db1f8e75864429b6; ?>
<?php unset($__componentOriginal623144f469b1e9f2db1f8e75864429b6); ?>
<?php endif; ?>
            <?php endif; ?>
        </div>

        <!-- Ribbon Container -->
        <div class="w-1/3 absolute right-0 top-0 p-3 h-32 flex flex-col justify-end rounded-bl-sm"
             style="background: <?php echo e($document->accentColor); ?>;">
            <?php if($document->header): ?>
                <h1 class="text-4xl font-bold text-white text-center uppercase"><?php echo e($document->header); ?></h1>
            <?php endif; ?>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal991b1314be9f8283186b0484b7191723)): ?>
<?php $attributes = $__attributesOriginal991b1314be9f8283186b0484b7191723; ?>
<?php unset($__attributesOriginal991b1314be9f8283186b0484b7191723); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal991b1314be9f8283186b0484b7191723)): ?>
<?php $component = $__componentOriginal991b1314be9f8283186b0484b7191723; ?>
<?php unset($__componentOriginal991b1314be9f8283186b0484b7191723); ?>
<?php endif; ?>

    <!-- Company Details -->
    <?php if (isset($component)) { $__componentOriginalca460fc25f005ce4e8cebd04395b16e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalca460fc25f005ce4e8cebd04395b16e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.document-template.metadata','data' => ['class' => 'modern-template-metadata space-y-8']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.document-template.metadata'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'modern-template-metadata space-y-8']); ?>
        <div class="text-sm">
            <strong class="text-sm block"><?php echo e($document->company->name); ?></strong>
            <?php if($formattedAddress = $document->company->getFormattedAddressHtml()): ?>
                <?php echo $formattedAddress; ?>

            <?php endif; ?>
        </div>

        <div class="flex justify-between items-end">
            <!-- Billing Details -->
            <div class="text-sm">
                <h3 class="text-gray-600 font-medium mb-1">BILL TO</h3>
                <p class="text-sm font-bold"
                   style="color: <?php echo e($document->accentColor); ?>"><?php echo e($document->client?->name ?? 'Client Not Found'); ?></p>

                <?php if($document->client && ($formattedAddress = $document->client->getFormattedAddressHtml())): ?>
                    <?php echo $formattedAddress; ?>

                <?php endif; ?>
            </div>

            <div class="text-sm">
                <table class="min-w-full">
                    <tbody>
                    <tr>
                        <td class="font-semibold text-right pr-2"><?php echo e($document->label->number); ?>:</td>
                        <td class="text-left pl-2"><?php echo e($document->number); ?></td>
                    </tr>
                    <?php if($document->referenceNumber): ?>
                        <tr>
                            <td class="font-semibold text-right pr-2"><?php echo e($document->label->referenceNumber); ?>:</td>
                            <td class="text-left pl-2"><?php echo e($document->referenceNumber); ?></td>
                        </tr>
                    <?php endif; ?>
                    <tr>
                        <td class="font-semibold text-right pr-2"><?php echo e($document->label->date); ?>:</td>
                        <td class="text-left pl-2"><?php echo e($document->date); ?></td>
                    </tr>
                    <tr>
                        <td class="font-semibold text-right pr-2"><?php echo e($document->label->dueDate); ?>:</td>
                        <td class="text-left pl-2"><?php echo e($document->dueDate); ?></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalca460fc25f005ce4e8cebd04395b16e2)): ?>
<?php $attributes = $__attributesOriginalca460fc25f005ce4e8cebd04395b16e2; ?>
<?php unset($__attributesOriginalca460fc25f005ce4e8cebd04395b16e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalca460fc25f005ce4e8cebd04395b16e2)): ?>
<?php $component = $__componentOriginalca460fc25f005ce4e8cebd04395b16e2; ?>
<?php unset($__componentOriginalca460fc25f005ce4e8cebd04395b16e2); ?>
<?php endif; ?>

    <!-- Line Items Table -->
    <?php if (isset($component)) { $__componentOriginald1c892b81ae5ab8242e101b2a9c8d98b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald1c892b81ae5ab8242e101b2a9c8d98b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.document-template.line-items','data' => ['class' => 'modern-template-line-items']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.document-template.line-items'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'modern-template-line-items']); ?>
        <table class="w-full text-left table-fixed">
            <thead class="text-sm leading-relaxed">
            <tr class="text-gray-600">
                <th class="text-left pl-6 w-[50%] py-4"><?php echo e($document->columnLabel->items); ?></th>
                <th class="text-center w-[10%] py-4"><?php echo e($document->columnLabel->units); ?></th>
                <th class="text-right w-[20%] py-4"><?php echo e($document->columnLabel->price); ?></th>
                <th class="text-right pr-6 w-[20%] py-4"><?php echo e($document->columnLabel->amount); ?></th>
            </tr>
            </thead>
            <tbody class="text-sm border-y-2">
            <?php $__currentLoopData = $document->lineItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr class="<?php echo \Illuminate\Support\Arr::toCssClasses(['bg-gray-100' => $index % 2 === 0]); ?>">
                    <td class="text-left pl-6 font-semibold py-3">
                        <?php echo e($item->name); ?>

                        <?php if($item->description): ?>
                            <div class="text-gray-600 font-normal line-clamp-2 mt-1"><?php echo e($item->description); ?></div>
                        <?php endif; ?>
                    </td>
                    <td class="text-center py-3"><?php echo e($item->quantity); ?></td>
                    <td class="text-right py-3"><?php echo e($item->unitPrice); ?></td>
                    <td class="text-right pr-6 py-3"><?php echo e($item->subtotal); ?></td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
            <tfoot class="text-sm summary-section">
            <?php if($document->subtotal): ?>
                <tr>
                    <td class="pl-6 py-2" colspan="2"></td>
                    <td class="text-right font-semibold py-2">Subtotal:</td>
                    <td class="text-right pr-6 py-2"><?php echo e($document->subtotal); ?></td>
                </tr>
            <?php endif; ?>
            <?php if($document->discount): ?>
                <tr class="text-success-800">
                    <td class="pl-6 py-2" colspan="2"></td>
                    <td class="text-right py-2">Discount:</td>
                    <td class="text-right pr-6 py-2">
                        (<?php echo e($document->discount); ?>)
                    </td>
                </tr>
            <?php endif; ?>
            <?php if($document->tax): ?>
                <tr>
                    <td class="pl-6 py-2" colspan="2"></td>
                    <td class="text-right py-2">Tax:</td>
                    <td class="text-right pr-6 py-2"><?php echo e($document->tax); ?></td>
                </tr>
            <?php endif; ?>
            <tr>
                <td class="pl-6 py-2" colspan="2"></td>
                <td class="text-right font-semibold border-t py-2"><?php echo e($document->amountDue ? 'Total' : 'Grand Total'); ?>:</td>
                <td class="text-right border-t pr-6 py-2"><?php echo e($document->total); ?></td>
            </tr>
            <?php if($document->amountDue): ?>
                <tr>
                    <td class="pl-6 py-2" colspan="2"></td>
                    <td class="text-right font-semibold border-t-4 border-double py-2"><?php echo e($document->label->amountDue); ?>

                        (<?php echo e($document->currencyCode); ?>):
                    </td>
                    <td class="text-right border-t-4 border-double pr-6 py-2"><?php echo e($document->amountDue); ?></td>
                </tr>
            <?php endif; ?>
            </tfoot>
        </table>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald1c892b81ae5ab8242e101b2a9c8d98b)): ?>
<?php $attributes = $__attributesOriginald1c892b81ae5ab8242e101b2a9c8d98b; ?>
<?php unset($__attributesOriginald1c892b81ae5ab8242e101b2a9c8d98b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald1c892b81ae5ab8242e101b2a9c8d98b)): ?>
<?php $component = $__componentOriginald1c892b81ae5ab8242e101b2a9c8d98b; ?>
<?php unset($__componentOriginald1c892b81ae5ab8242e101b2a9c8d98b); ?>
<?php endif; ?>

    <!-- Footer Notes -->
    <?php if (isset($component)) { $__componentOriginalb3f7682bed2a6d9d3c3afe2b0fa8f32b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb3f7682bed2a6d9d3c3afe2b0fa8f32b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.document-template.footer','data' => ['class' => 'modern-template-footer']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.document-template.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'modern-template-footer']); ?>
        <h4 class="font-semibold px-6 text-sm" style="color: <?php echo e($document->accentColor); ?>">
            Terms & Conditions
        </h4>
        <span class="border-t-2 my-2 border-gray-300 block w-full"></span>
        <div class="flex justify-between space-x-4 px-6 text-sm">
            <p class="w-1/2 break-words line-clamp-4"><?php echo e($document->terms); ?></p>
            <p class="w-1/2 break-words line-clamp-4"><?php echo e($document->footer); ?></p>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb3f7682bed2a6d9d3c3afe2b0fa8f32b)): ?>
<?php $attributes = $__attributesOriginalb3f7682bed2a6d9d3c3afe2b0fa8f32b; ?>
<?php unset($__attributesOriginalb3f7682bed2a6d9d3c3afe2b0fa8f32b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb3f7682bed2a6d9d3c3afe2b0fa8f32b)): ?>
<?php $component = $__componentOriginalb3f7682bed2a6d9d3c3afe2b0fa8f32b; ?>
<?php unset($__componentOriginalb3f7682bed2a6d9d3c3afe2b0fa8f32b); ?>
<?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal21b9196bf26e13953fd5bee32e75c09f)): ?>
<?php $attributes = $__attributesOriginal21b9196bf26e13953fd5bee32e75c09f; ?>
<?php unset($__attributesOriginal21b9196bf26e13953fd5bee32e75c09f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal21b9196bf26e13953fd5bee32e75c09f)): ?>
<?php $component = $__componentOriginal21b9196bf26e13953fd5bee32e75c09f; ?>
<?php unset($__componentOriginal21b9196bf26e13953fd5bee32e75c09f); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\filament\company\components\document-templates\modern.blade.php ENDPATH**/ ?>