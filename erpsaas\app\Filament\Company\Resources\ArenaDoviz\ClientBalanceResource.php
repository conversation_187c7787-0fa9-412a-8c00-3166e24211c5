<?php

namespace App\Filament\Company\Resources\ArenaDoviz;

use App\Filament\Company\Resources\ArenaDoviz\ClientBalanceResource\Pages;
use App\Models\ArenaDoviz\ClientBalance;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ClientBalanceResource extends Resource
{
    protected static ?string $model = ClientBalance::class;
    protected static ?string $navigationIcon = 'heroicon-o-scale';
    protected static ?string $navigationLabel = 'Client Balances';
    protected static ?string $modelLabel = 'Client Balance';
    protected static ?string $pluralModelLabel = 'Client Balances';
    protected static ?string $navigationGroup = 'Arena Doviz';
    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('client_id')
                    ->relationship('client', 'name')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->live(),

                Forms\Components\Select::make('currency_code')
                    ->required()
                    ->options([
                        'USD' => 'US Dollar (USD)',
                        'EUR' => 'Euro (EUR)',
                        'TRY' => 'Turkish Lira (TRY)',
                        'GBP' => 'British Pound (GBP)',
                        'AED' => 'UAE Dirham (AED)',
                        'IRR' => 'Iranian Rial (IRR)',
                    ])
                    ->searchable()
                    ->live()
                    ->afterStateUpdated(function ($state, $get, $set) {
                        $clientId = $get('client_id');
                        if ($clientId && $state) {
                            $existing = \App\Models\ArenaDoviz\ClientBalance::where([
                                'company_id' => auth()->user()->currentCompany->id,
                                'client_id' => $clientId,
                                'currency_code' => $state,
                            ])->first();

                            if ($existing) {
                                $set('balance', $existing->balance);
                                $set('reserved_balance', $existing->reserved_balance);
                            }
                        }
                    }),

                Forms\Components\TextInput::make('balance')
                    ->required()
                    ->numeric()
                    ->step(0.0001)
                    ->helperText('Current balance for this client in the selected currency'),

                Forms\Components\TextInput::make('reserved_balance')
                    ->numeric()
                    ->step(0.0001)
                    ->default(0)
                    ->helperText('Amount reserved for pending transactions'),

                Forms\Components\Placeholder::make('available_balance_display')
                    ->label('Available Balance')
                    ->content(function ($get) {
                        $balance = (float) ($get('balance') ?? 0);
                        $reserved = (float) ($get('reserved_balance') ?? 0);
                        $available = $balance - $reserved;
                        return number_format($available, 4);
                    })
                    ->visible(fn ($get) => $get('balance') !== null || $get('reserved_balance') !== null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('client.name')
                    ->label('Client')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('currency_code')
                    ->label('Currency')
                    ->badge()
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('balance')
                    ->label('Balance')
                    ->money('TRY')
                    ->sortable()
                    ->color(fn($record) => $record->balance >= 0 ? 'success' : 'danger'),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Last Updated')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('currency_code')
                    ->options([
                        'TRY' => 'Turkish Lira (TRY)',
                        'USD' => 'US Dollar (USD)',
                        'EUR' => 'Euro (EUR)',
                        'GBP' => 'British Pound (GBP)',
                        'AED' => 'UAE Dirham (AED)',
                    ]),

                Tables\Filters\Filter::make('negative_balance')
                    ->query(fn (Builder $query): Builder => $query->where('balance', '<', 0))
                    ->label('Negative Balances'),

                Tables\Filters\Filter::make('positive_balance')
                    ->query(fn (Builder $query): Builder => $query->where('balance', '>', 0))
                    ->label('Positive Balances'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('updated_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListClientBalances::route('/'),
            'create' => Pages\CreateClientBalance::route('/create'),
            'view' => Pages\ViewClientBalance::route('/{record}'),
            'edit' => Pages\EditClientBalance::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('balance', '<', 0)->count() ?: null;
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return static::getModel()::where('balance', '<', 0)->count() > 0 ? 'danger' : null;
    }
}
