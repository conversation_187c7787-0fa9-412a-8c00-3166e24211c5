<?php
    use Filament\Support\Enums\IconPosition;
    use Filament\Support\Enums\Alignment;
    use Filament\Support\Enums\IconSize;

    $id = $getId();
    $isDisabled = $isDisabled();
    $isMultiple = $isMultiple();
    $statePath = $getStatePath();
?>

<?php if (isset($component)) { $__componentOriginal511d4862ff04963c3c16115c05a86a9d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal511d4862ff04963c3c16115c05a86a9d = $attributes; } ?>
<?php $component = Illuminate\View\DynamicComponent::resolve(['component' => $getFieldWrapperView()] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dynamic-component'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\DynamicComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['field' => $field]); ?>
    <?php if (isset($component)) { $__componentOriginal30dbd75eb120a380110a2b340cd88f46 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal30dbd75eb120a380110a2b340cd88f46 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.grid.index','data' => ['default' => $getColumns('default'),'sm' => $getColumns('sm'),'md' => $getColumns('md'),'lg' => $getColumns('lg'),'xl' => $getColumns('xl'),'twoXl' => $getColumns('2xl'),'isGrid' => true,'class' => \Illuminate\Support\Arr::toCssClasses(['gap-5'])]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::grid'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['default' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($getColumns('default')),'sm' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($getColumns('sm')),'md' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($getColumns('md')),'lg' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($getColumns('lg')),'xl' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($getColumns('xl')),'two-xl' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($getColumns('2xl')),'is-grid' => true,'class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(\Illuminate\Support\Arr::toCssClasses(['gap-5']))]); ?>
        <?php $__currentLoopData = $getOptions(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
                $shouldOptionBeDisabled = $isDisabled || $isOptionDisabled($value, $label);
            ?>

            <label class="flex cursor-pointer gap-x-3">
                <input <?php if($shouldOptionBeDisabled): echo 'disabled'; endif; ?> id="<?php echo e($id); ?>-<?php echo e($value); ?>"
                       <?php if(! $isMultiple): ?>
                           name="<?php echo e($id); ?>"
                       <?php endif; ?>
                       type="<?php echo e($isMultiple ? 'checkbox' : 'radio'); ?>"
                       value="<?php echo e($value); ?>" wire:loading.attr="disabled"
                <?php echo e($applyStateBindingModifiers('wire:model')); ?>="<?php echo e($statePath); ?>"
                <?php echo e($getExtraInputAttributeBag()->class(['peer hidden'])); ?> />

                <?php
                    $iconExists = $hasIcons($value);
                    $iconPosition = $getIconPosition();
                    $alignment = $getAlignment();
                    $direction = $getDirection();
                    $gap = $getGap();
                    $padding = $getPadding();
                    $color = $getColor();
                    $icon = $getIcon($value);
                    $iconSize = $getIconSize();
                    $iconSizeSm = $getIconSizes('sm');
                    $iconSizeMd = $getIconSizes('md');
                    $iconSizeLg = $getIconSizes('lg');
                    $descriptionExists = $hasDescription($value);
                    $description = $getDescription($value);
                ?>
                <div <?php echo e($getExtraCardsAttributeBag()->class([
                    'flex w-full text-sm leading-6 rounded-lg bg-white dark:bg-gray-900',
                    $padding ?: 'px-4 py-2',
                    $gap ?: 'gap-5',
                    match ($direction) {
                        'column' => 'flex-col',
                        default => 'flex-row',
                    },
                    $iconExists
                        ? match ($iconPosition) {
                            IconPosition::Before, 'before' => 'justify-start',
                            IconPosition::After, 'after' => 'justify-between flex-row-reverse',
                            default => 'justify-start',
                        }
                        : 'justify-start',
                    match ($alignment) {
                        Alignment::Center, 'center' => 'items-center',
                        Alignment::Start, 'start' => 'items-start',
                        Alignment::End, 'end' => 'items-end',
                        default => 'items-center',
                    },
                    'ring-1 ring-gray-950/10 dark:ring-white/20 peer-checked:ring-2',
                    'peer-disabled:bg-gray-100/50 dark:peer-disabled:bg-gray-700/50 peer-disabled:cursor-not-allowed',
                    match ($color) {
                        'gray' => 'peer-checked:ring-gray-600 dark:peer-checked:ring-gray-500',
                        default
                            => 'fi-color-custom peer-checked:ring-custom-600 dark:peer-checked:ring-custom-500',
                    },
                ])); ?> style="<?php echo \Illuminate\Support\Arr::toCssStyles([
                    \Filament\Support\get_color_css_variables($color, shades: [600, 500]) => $color !== 'gray',
                ]) ?>">
                    <?php if($iconExists): ?>
                        <?php if (isset($component)) { $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.icon','data' => ['icon' => $icon,'class' => \Illuminate\Support\Arr::toCssClasses([
                            'flex-shrink-0',
                            match ($iconSize) {
                                IconSize::Small => $iconSizeSm ?: 'h-8 w-8',
                                'sm' => $iconSizeSm ?: 'h-8 w-8',
                                IconSize::Medium => $iconSizeMd ?: 'h-9 w-9',
                                'md' => $iconSizeMd ?: 'h-9 w-9',
                                IconSize::Large => $iconSizeLg ?: 'h-10 w-10',
                                'lg' => $iconSizeLg ?: 'h-10 w-10',
                                default => 'h-8 w-8',
                            },
                            match ($color) {
                                'gray' => 'fi-color-gray text-gray-600 dark:text-gray-500',
                                default => 'fi-color-custom text-custom-600 dark:text-custom-500',
                            },
                        ]),'style' => \Illuminate\Support\Arr::toCssStyles([
                            \Filament\Support\get_color_css_variables($color, shades: [600, 500]) => $color !== 'gray',
                        ])]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($icon),'class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(\Illuminate\Support\Arr::toCssClasses([
                            'flex-shrink-0',
                            match ($iconSize) {
                                IconSize::Small => $iconSizeSm ?: 'h-8 w-8',
                                'sm' => $iconSizeSm ?: 'h-8 w-8',
                                IconSize::Medium => $iconSizeMd ?: 'h-9 w-9',
                                'md' => $iconSizeMd ?: 'h-9 w-9',
                                IconSize::Large => $iconSizeLg ?: 'h-10 w-10',
                                'lg' => $iconSizeLg ?: 'h-10 w-10',
                                default => 'h-8 w-8',
                            },
                            match ($color) {
                                'gray' => 'fi-color-gray text-gray-600 dark:text-gray-500',
                                default => 'fi-color-custom text-custom-600 dark:text-custom-500',
                            },
                        ])),'style' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(\Illuminate\Support\Arr::toCssStyles([
                            \Filament\Support\get_color_css_variables($color, shades: [600, 500]) => $color !== 'gray',
                        ]))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $attributes = $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $component = $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
                    <?php endif; ?>
                    <div <?php echo e($getExtraOptionsAttributeBag()->merge(['class' =>'place-items-start'])); ?>>
                        <span class="font-medium text-gray-950 dark:text-white">
                            <?php echo e($label); ?>

                        </span>

                        <?php if($descriptionExists): ?>
                            <p <?php echo e($getExtraDescriptionsAttributeBag()->merge(['class' =>'text-gray-500 dark:text-gray-400'])); ?>>
                                <?php echo e($description); ?>

                            </p>
                        <?php endif; ?>
                    </div>
                </div>
            </label>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal30dbd75eb120a380110a2b340cd88f46)): ?>
<?php $attributes = $__attributesOriginal30dbd75eb120a380110a2b340cd88f46; ?>
<?php unset($__attributesOriginal30dbd75eb120a380110a2b340cd88f46); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal30dbd75eb120a380110a2b340cd88f46)): ?>
<?php $component = $__componentOriginal30dbd75eb120a380110a2b340cd88f46; ?>
<?php unset($__componentOriginal30dbd75eb120a380110a2b340cd88f46); ?>
<?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal511d4862ff04963c3c16115c05a86a9d)): ?>
<?php $attributes = $__attributesOriginal511d4862ff04963c3c16115c05a86a9d; ?>
<?php unset($__attributesOriginal511d4862ff04963c3c16115c05a86a9d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal511d4862ff04963c3c16115c05a86a9d)): ?>
<?php $component = $__componentOriginal511d4862ff04963c3c16115c05a86a9d; ?>
<?php unset($__componentOriginal511d4862ff04963c3c16115c05a86a9d); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\vendor\radio-deck\forms\components\radio-deck.blade.php ENDPATH**/ ?>