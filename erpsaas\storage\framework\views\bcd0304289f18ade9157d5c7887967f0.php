<?php if (isset($component)) { $__componentOriginal511d4862ff04963c3c16115c05a86a9d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal511d4862ff04963c3c16115c05a86a9d = $attributes; } ?>
<?php $component = Illuminate\View\DynamicComponent::resolve(['component' => $getFieldWrapperView()] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dynamic-component'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\DynamicComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['field' => $field]); ?>
    <?php
        $id = $getId();
        $statePath = $getStatePath();
        $isDisabled = $isDisabled();
        $onLabel = $getOnLabel();
        $offLabel = $getOffLabel();
        $onColor = $getOnColor() ?? 'primary';
        $offColor = $getOffColor() ?? 'danger';
        $buttons = [
            'on' => [$onColor, $onLabel, true],
            'off' => [$offColor, $offLabel, false]
        ];
    ?>

    <div
        class="flex items-center"
        x-data="{
            state: $wire.<?php echo e($applyStateBindingModifiers("entangle('{$statePath}')")); ?>

        }"
        x-on:keydown.left="state = true"
        x-on:keydown.right="state = false"
    >
        <?php $__currentLoopData = $buttons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => [$color, $label, $value]): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <button
                x-on:click="state = <?php echo e($value ? 'true' : 'false'); ?>"
                x-bind:aria-pressed="state ? 'true' : 'false'"
                x-bind:class="
                    <?php echo e($value ? 'state' : '!state'); ?>

                        ? '<?php echo e(match ($color) {
                                'gray' => 'bg-gray-700 text-white hover:bg-gray-700 dark:bg-gray-950 dark:text-white dark:hover:bg-gray-800',
                                default => 'bg-custom-600 text-white hover:bg-custom-500 dark:bg-custom-500 dark:hover:bg-custom-400',
                            }); ?>'
                        : 'bg-gray-200 dark:bg-white/10 text-gray-900 dark:text-white'
                "
                x-bind:style="
                    <?php echo e($value ? 'state' : '!state'); ?>

                        ? '<?php echo e(\Filament\Support\get_color_css_variables($color, shades: [600, 500, 400])); ?>'
                        : null
                "
                <?php echo e($attributes
                    ->merge([
                        'id' => "{$id}-{$label}",
                        'role' => 'button',
                        'type' => 'button',
                        'aria-label' => $label,
                        'disabled' => $isDisabled,
                        'wire:loading.attr' => 'disabled',
                    ], escape: false)
                    ->merge($getExtraAttributes(), escape: false)
                    ->merge($getExtraAlpineAttributes(), escape: false)
                    ->class([
                        'relative inline-block outline-none cursor-pointer text-base sm:text-sm sm:leading-6 text-center ps-3 pe-3 py-1.5 transition-all duration-200 ease-in-out disabled:pointer-events-none disabled:opacity-70',
                        'selectify-toggle-on rounded-s-lg' => $key === 'on',
                        'selectify-toggle-off rounded-e-lg' => $key === 'off',
                    ])); ?>

            >
                <?php echo e($label); ?>

            </button>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal511d4862ff04963c3c16115c05a86a9d)): ?>
<?php $attributes = $__attributesOriginal511d4862ff04963c3c16115c05a86a9d; ?>
<?php unset($__attributesOriginal511d4862ff04963c3c16115c05a86a9d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal511d4862ff04963c3c16115c05a86a9d)): ?>
<?php $component = $__componentOriginal511d4862ff04963c3c16115c05a86a9d; ?>
<?php unset($__componentOriginal511d4862ff04963c3c16115c05a86a9d); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\vendor\andrewdwallo\filament-selectify\resources\views\components\toggle-button.blade.php ENDPATH**/ ?>