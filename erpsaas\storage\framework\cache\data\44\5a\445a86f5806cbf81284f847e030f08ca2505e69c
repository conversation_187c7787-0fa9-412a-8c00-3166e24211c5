1756292114O:46:"App\Transformers\TrialBalanceReportTransformer":1:{s:9:" * report";O:17:"App\DTO\ReportDTO":10:{s:10:"categories";a:5:{s:6:"Assets";O:26:"App\DTO\AccountCategoryDTO":3:{s:8:"accounts";a:0:{}s:5:"types";N;s:7:"summary";O:25:"App\DTO\AccountBalanceDTO":5:{s:15:"startingBalance";N;s:12:"debitBalance";s:6:"A$0.00";s:13:"creditBalance";s:6:"A$0.00";s:11:"netMovement";N;s:13:"endingBalance";N;}}s:11:"Liabilities";O:26:"App\DTO\AccountCategoryDTO":3:{s:8:"accounts";a:0:{}s:5:"types";N;s:7:"summary";O:25:"App\DTO\AccountBalanceDTO":5:{s:15:"startingBalance";N;s:12:"debitBalance";s:6:"A$0.00";s:13:"creditBalance";s:6:"A$0.00";s:11:"netMovement";N;s:13:"endingBalance";N;}}s:6:"Equity";O:26:"App\DTO\AccountCategoryDTO":3:{s:8:"accounts";a:0:{}s:5:"types";N;s:7:"summary";O:25:"App\DTO\AccountBalanceDTO":5:{s:15:"startingBalance";N;s:12:"debitBalance";s:6:"A$0.00";s:13:"creditBalance";s:6:"A$0.00";s:11:"netMovement";N;s:13:"endingBalance";N;}}s:7:"Revenue";O:26:"App\DTO\AccountCategoryDTO":3:{s:8:"accounts";a:0:{}s:5:"types";N;s:7:"summary";O:25:"App\DTO\AccountBalanceDTO":5:{s:15:"startingBalance";N;s:12:"debitBalance";s:6:"A$0.00";s:13:"creditBalance";s:6:"A$0.00";s:11:"netMovement";N;s:13:"endingBalance";N;}}s:8:"Expenses";O:26:"App\DTO\AccountCategoryDTO":3:{s:8:"accounts";a:0:{}s:5:"types";N;s:7:"summary";O:25:"App\DTO\AccountBalanceDTO":5:{s:15:"startingBalance";N;s:12:"debitBalance";s:6:"A$0.00";s:13:"creditBalance";s:6:"A$0.00";s:11:"netMovement";N;s:13:"endingBalance";N;}}}s:12:"overallTotal";O:25:"App\DTO\AccountBalanceDTO":5:{s:15:"startingBalance";N;s:12:"debitBalance";s:6:"A$0.00";s:13:"creditBalance";s:6:"A$0.00";s:11:"netMovement";N;s:13:"endingBalance";N;}s:12:"agingSummary";N;s:18:"entityBalanceTotal";N;s:21:"overallPaymentMetrics";N;s:6:"fields";a:3:{i:0;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:0;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";E:37:"Filament\Support\Enums\Alignment:Left";s:8:" * label";s:8:"ACCOUNTS";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:12:"account_name";}i:1;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:0;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";E:38:"Filament\Support\Enums\Alignment:Right";s:8:" * label";s:5:"DEBIT";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:13:"debit_balance";}i:2;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:0;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";r:79;s:8:" * label";s:6:"CREDIT";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:14:"credit_balance";}}s:10:"reportType";s:8:"standard";s:8:"overview";N;s:9:"startDate";N;s:7:"endDate";N;}}