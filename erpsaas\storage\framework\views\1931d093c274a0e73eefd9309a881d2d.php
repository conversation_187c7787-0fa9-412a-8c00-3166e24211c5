<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="flex flex-col gap-y-6">
        <?php if (isset($component)) { $__componentOriginal447636fe67a19f9c79619fb5a3c0c28d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal447636fe67a19f9c79619fb5a3c0c28d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.tabs.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::tabs'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
            <?php $__currentLoopData = $this->accountCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $categoryValue => $accountSubtypes): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if (isset($component)) { $__componentOriginal35d4caf141547fb7d125e4ebd3c1b66f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal35d4caf141547fb7d125e4ebd3c1b66f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.tabs.item','data' => ['wire:key' => 'tab-item-'.e($categoryValue).'','active' => $activeTab === $categoryValue,'wire:click' => '$set(\'activeTab\', \''.e($categoryValue).'\')','badge' => $accountSubtypes->sum('accounts_count')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::tabs.item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:key' => 'tab-item-'.e($categoryValue).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($activeTab === $categoryValue),'wire:click' => '$set(\'activeTab\', \''.e($categoryValue).'\')','badge' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($accountSubtypes->sum('accounts_count'))]); ?>
                    <?php echo e($this->getCategoryLabel($categoryValue)); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal35d4caf141547fb7d125e4ebd3c1b66f)): ?>
<?php $attributes = $__attributesOriginal35d4caf141547fb7d125e4ebd3c1b66f; ?>
<?php unset($__attributesOriginal35d4caf141547fb7d125e4ebd3c1b66f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal35d4caf141547fb7d125e4ebd3c1b66f)): ?>
<?php $component = $__componentOriginal35d4caf141547fb7d125e4ebd3c1b66f; ?>
<?php unset($__componentOriginal35d4caf141547fb7d125e4ebd3c1b66f); ?>
<?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal447636fe67a19f9c79619fb5a3c0c28d)): ?>
<?php $attributes = $__attributesOriginal447636fe67a19f9c79619fb5a3c0c28d; ?>
<?php unset($__attributesOriginal447636fe67a19f9c79619fb5a3c0c28d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal447636fe67a19f9c79619fb5a3c0c28d)): ?>
<?php $component = $__componentOriginal447636fe67a19f9c79619fb5a3c0c28d; ?>
<?php unset($__componentOriginal447636fe67a19f9c79619fb5a3c0c28d); ?>
<?php endif; ?>

        <?php $__currentLoopData = $this->accountCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $categoryValue => $accountSubtypes): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if($activeTab === $categoryValue): ?>
                <div
                    class="es-table__container overflow-hidden rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:divide-white/10 dark:bg-gray-900 dark:ring-white/10">
                    <div class="es-table__header-ctn"></div>
                    <div class="es-table__content overflow-x-auto">
                        <table
                            class="es-table w-full min-w-[70rem] divide-y divide-gray-200 text-start text-sm dark:divide-white/5">
                            <colgroup>
                                <col span="1" style="width: 12.5%;">
                                <col span="1" style="width: 20%;">
                                <col span="1" style="width: 35%;">
                                <col span="1" style="width: 15%;">
                                <col span="1" style="width: 10%;">
                                <col span="1" style="width: 7.5%;">
                            </colgroup>
                            <?php $__currentLoopData = $accountSubtypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $accountSubtype): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tbody
                                    class="es-table__rowgroup divide-y divide-gray-200 whitespace-nowrap dark:divide-white/5">
                                <!-- Subtype Name Header Row -->
                                <tr class="es-table__row--header bg-gray-50 dark:bg-white/5">
                                    <td colspan="6" class="es-table__cell px-4 py-4">
                                        <div class="es-table__row-content flex items-center space-x-2">
                                            <span
                                                class="es-table__row-title text-gray-800 dark:text-gray-200 font-semibold tracking-wider">
                                                <?php echo e($accountSubtype->name); ?>

                                            </span>
                                            <?php if (isset($component)) { $__componentOriginal7875b222dc4d64f17fd6d2e345da8799 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7875b222dc4d64f17fd6d2e345da8799 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.tooltip','data' => ['text' => ''.$accountSubtype->description.'','icon' => 'heroicon-o-question-mark-circle','placement' => 'right','maxWidth' => '300']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('tooltip'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['text' => ''.$accountSubtype->description.'','icon' => 'heroicon-o-question-mark-circle','placement' => 'right','maxWidth' => '300']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7875b222dc4d64f17fd6d2e345da8799)): ?>
<?php $attributes = $__attributesOriginal7875b222dc4d64f17fd6d2e345da8799; ?>
<?php unset($__attributesOriginal7875b222dc4d64f17fd6d2e345da8799); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7875b222dc4d64f17fd6d2e345da8799)): ?>
<?php $component = $__componentOriginal7875b222dc4d64f17fd6d2e345da8799; ?>
<?php unset($__componentOriginal7875b222dc4d64f17fd6d2e345da8799); ?>
<?php endif; ?>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Chart Rows -->
                                <?php $__empty_1 = true; $__currentLoopData = $accountSubtype->accounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr class="es-table__row">
                                        <td colspan="1" class="es-table__cell px-4 py-4"><?php echo e($account->code); ?></td>
                                        <td colspan="1" class="es-table__cell px-4 py-4">
                                            <?php echo e($account->name); ?>

                                            <br>
                                            <small class="text-gray-500 dark:text-gray-400">
                                                <?php if($account->last_transaction_date): ?>
                                                    Last transaction
                                                    on <?php echo e(\Illuminate\Support\Carbon::parse($account->last_transaction_date)->toDefaultDateFormat()); ?>

                                                <?php else: ?>
                                                    No transactions for this account
                                                <?php endif; ?>
                                            </small>
                                        </td>
                                        <td colspan="2"
                                            class="es-table__cell px-4 py-4"><?php echo e($account->description); ?></td>
                                        <td colspan="1" class="es-table__cell px-4 py-4">
                                            <?php if($account->archived): ?>
                                                <?php if (isset($component)) { $__componentOriginal986dce9114ddce94a270ab00ce6c273d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal986dce9114ddce94a270ab00ce6c273d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.badge','data' => ['color' => 'gray','size' => 'sm']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::badge'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['color' => 'gray','size' => 'sm']); ?>
                                                    Archived
                                                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal986dce9114ddce94a270ab00ce6c273d)): ?>
<?php $attributes = $__attributesOriginal986dce9114ddce94a270ab00ce6c273d; ?>
<?php unset($__attributesOriginal986dce9114ddce94a270ab00ce6c273d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal986dce9114ddce94a270ab00ce6c273d)): ?>
<?php $component = $__componentOriginal986dce9114ddce94a270ab00ce6c273d; ?>
<?php unset($__componentOriginal986dce9114ddce94a270ab00ce6c273d); ?>
<?php endif; ?>
                                            <?php endif; ?>
                                        </td>
                                        <td colspan="1" class="es-table__cell px-4 py-4">
                                            <div>
                                                <?php if($account->default === false && !$account->adjustment): ?>
                                                    <?php echo e(($this->editAccountAction)(['account' => $account->id])); ?>

                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <!-- No Accounts Available Row -->
                                    <tr class="es-table__row">
                                        <td colspan="5"
                                            class="es-table__cell px-4 py-4 italic text-xs text-gray-500 dark:text-gray-400">
                                            <?php echo e(__("You haven't added any {$accountSubtype->name} accounts yet.")); ?>

                                        </td>
                                    </tr>
                                <?php endif; ?>

                                <!-- Add New Account Row -->
                                <tr class="es-table__row">
                                    <td colspan="5" class="es-table__cell px-4 py-4">
                                        <?php echo e(($this->createAccountAction)(['accountSubtype' => $accountSubtype->id])); ?>

                                    </td>
                                </tr>
                                </tbody>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </table>
                    </div>
                    <div class="es-table__footer-ctn border-t border-gray-200"></div>
                </div>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\filament\company\pages\accounting\chart.blade.php ENDPATH**/ ?>