<?php $__env->startSection('content'); ?>
    <div class="header">
        <div class="title"><?php echo e($report->getTitle()); ?></div>
        <div class="company-name"><?php echo e($company->name); ?></div>
        <div class="date-range">Date Range: <?php echo e($startDate); ?> to <?php echo e($endDate); ?></div>
    </div>
    <table class="table-class">
        <thead class="table-head">
        <tr>
            <?php $__currentLoopData = $report->getHeaders(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $header): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <th class="<?php echo e($report->getAlignmentClass($index)); ?>">
                    <?php echo e($header); ?>

                </th>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tr>
        </thead>
        <?php $__currentLoopData = $report->getCategories(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tbody>
            <tr class="category-header-row">
                <td colspan="<?php echo e(count($report->getHeaders())); ?>">
                    <div>
                        <?php $__currentLoopData = $category->header; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $headerRow): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div>
                                <?php $__currentLoopData = $headerRow; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $headerValue): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if(!empty($headerValue)): ?>
                                        <?php echo e($headerValue); ?>

                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </td>
            </tr>
            <?php $__currentLoopData = $category->data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dataIndex => $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr
                    class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                        'category-header-row' => $loop->first || $loop->last || $loop->remaining === 1,
                    ]); ?>">
                    <?php $__currentLoopData = $transaction; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cellIndex => $cell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <td class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                            $report->getAlignmentClass($cellIndex),
                            'whitespace-normal' => $cellIndex === 'description',
                            'whitespace-nowrap' => $cellIndex !== 'description',
                        ]); ?>"
                        >
                            <?php if(is_array($cell) && isset($cell['description'])): ?>
                                <?php echo e($cell['description']); ?>

                            <?php else: ?>
                                <?php echo e($cell); ?>

                            <?php endif; ?>
                        </td>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php if (! ($loop->last)): ?>
                <tr class="spacer-row">
                    <td colspan="<?php echo e(count($report->getHeaders())); ?>"></td>
                </tr>
            <?php endif; ?>
            </tbody>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </table>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('components.company.reports.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\company\reports\account-transactions-report-pdf.blade.php ENDPATH**/ ?>