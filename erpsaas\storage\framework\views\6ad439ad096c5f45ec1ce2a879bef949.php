<div>
    <div class="grid grid-cols-1 gap-4">
        <?php $__empty_1 = true; $__currentLoopData = $this->connectedInstitutions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $institution): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <section class="connected-account-section overflow-hidden rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
                <header class="connected-account-header bg-primary-300/10 px-6 py-4 flex flex-col sm:flex-row sm:items-center gap-3">
                    <?php if($institution->logo_url === null): ?>
                        <div class="flex-shrink-0 bg-platinum p-2 rounded-full dark:bg-gray-500/20">
                            <?php if (isset($component)) { $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.icon','data' => ['icon' => 'heroicon-o-building-library','class' => 'h-6 w-6 text-gray-500 dark:text-gray-400']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'heroicon-o-building-library','class' => 'h-6 w-6 text-gray-500 dark:text-gray-400']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $attributes = $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $component = $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
                        </div>
                    <?php else: ?>
                        <img
                            src="<?php echo e($institution->logo_url); ?>"
                            alt="<?php echo e($institution->name); ?>"
                            class="h-10 object-contain object-left"
                        >
                    <?php endif; ?>

                    <div class="flex-auto">
                        <h3 class="connected-account-section-header-heading text-lg font-semibold leading-6 text-gray-950 dark:text-white">
                            <?php echo e($institution->name); ?>

                        </h3>

                        <?php if($institution->latestImport): ?>
                            <p class="connected-account-section-header-description text-sm leading-6 text-gray-500 dark:text-gray-400">
                                <?php echo e(__('Last updated')); ?> <?php echo e($institution->latestImport->last_imported_at->diffForHumans()); ?>

                            </p>
                        <?php endif; ?>
                    </div>

                    
                    <?php if($institution->getEnabledConnectedBankAccounts()->isNotEmpty()): ?>
                        <?php echo e(($this->refreshTransactions)(['institution' => $institution->id])); ?>

                    <?php endif; ?>

                    
                    <?php echo e(($this->deleteBankConnection)(['institution' => $institution->id])); ?>

                </header>

                <?php $__currentLoopData = $institution->connectedBankAccounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $connectedBankAccount): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
                        $account = $connectedBankAccount->bankAccount?->account;
                    ?>
                    <div class="border-t-2 border-gray-200 dark:border-white/10 px-6 py-4">
                        <div class="flex flex-col sm:flex-row items-start gap-y-2">
                            <div class="grid flex-auto gap-y-2">
                                <span class="account-name text-base font-medium leading-6 text-gray-900 dark:text-white">
                                    <?php echo e($connectedBankAccount->name); ?>

                                </span>
                                <span class="account-type text-sm leading-6 text-gray-600 dark:text-gray-200">
                                    <?php echo e(ucwords($connectedBankAccount->subtype)); ?> <?php echo e($connectedBankAccount->masked_number); ?>

                                </span>
                            </div>

                            <?php if($account?->ending_balance): ?>
                                <div class="account-balance flex text-base leading-6 text-gray-700 dark:text-gray-200 space-x-1">
                                    <strong wire:poll.visible><?php echo e($account->ending_balance->format()); ?></strong>
                                    <p><?php echo e($account->currency_code); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>

                        
                        <div class="mt-4">
                            <?php if($connectedBankAccount->import_transactions): ?>
                                <?php echo e(($this->stopImportingTransactions)(['connectedBankAccount' => $connectedBankAccount->id])); ?>

                            <?php else: ?>
                                <?php echo e(($this->startImportingTransactions)(['connectedBankAccount' => $connectedBankAccount->id])); ?>

                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </section>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <section class="connected-account-section overflow-hidden rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
                <div class="px-6 py-12 text-center">
                    <div class="connected-account-empty-state-content mx-auto grid max-w-lg justify-items-center text-center">
                        <div class="connected-account-empty-state-icon-ctn mb-4 rounded-full bg-platinum p-3 dark:bg-gray-500/20">
                            <?php if (isset($component)) { $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.icon','data' => ['icon' => 'heroicon-o-x-mark','class' => 'connected-account-empty-state-icon h-6 w-6 text-gray-500 dark:text-gray-400']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'heroicon-o-x-mark','class' => 'connected-account-empty-state-icon h-6 w-6 text-gray-500 dark:text-gray-400']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $attributes = $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $component = $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
                        </div>
                        <h4 class="connected-account-empty-state-heading text-base font-semibold leading-6 text-gray-950 dark:text-white">
                            <?php echo e(__('No connected accounts')); ?>

                        </h4>
                        <p class="connected-account-empty-state-description text-sm text-gray-500 dark:text-gray-400 mt-1">
                            <?php echo e(__('Connect your bank account to get started.')); ?>

                        </p>
                        <div class="connected-account-empty-state-action flex shrink-0 items-center gap-3 flex-wrap justify-center mt-6">
                            <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['wire:click' => '$dispatch(\'createToken\')','wire:loading.attr' => 'disabled']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:click' => '$dispatch(\'createToken\')','wire:loading.attr' => 'disabled']); ?>
                                <?php echo e(__('Connect account')); ?>

                             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
                        </div>
                    </div>
                </div>
            </section>
        <?php endif; ?>

        <?php if (isset($component)) { $__componentOriginal028e05680f6c5b1e293abd7fbe5f9758 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal028e05680f6c5b1e293abd7fbe5f9758 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-actions::components.modals','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-actions::modals'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal028e05680f6c5b1e293abd7fbe5f9758)): ?>
<?php $attributes = $__attributesOriginal028e05680f6c5b1e293abd7fbe5f9758; ?>
<?php unset($__attributesOriginal028e05680f6c5b1e293abd7fbe5f9758); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal028e05680f6c5b1e293abd7fbe5f9758)): ?>
<?php $component = $__componentOriginal028e05680f6c5b1e293abd7fbe5f9758; ?>
<?php unset($__componentOriginal028e05680f6c5b1e293abd7fbe5f9758); ?>
<?php endif; ?>
    </div>
    
        <?php
        $__assetKey = '3265081075-0';

        ob_start();
    ?>
    <script src="https://cdn.plaid.com/link/v2/stable/link-initialize.js"></script>
        <?php
        $__output = ob_get_clean();

        // If the asset has already been loaded anywhere during this request, skip it...
        if (in_array($__assetKey, \Livewire\Features\SupportScriptsAndAssets\SupportScriptsAndAssets::$alreadyRunAssetKeys)) {
            // Skip it...
        } else {
            \Livewire\Features\SupportScriptsAndAssets\SupportScriptsAndAssets::$alreadyRunAssetKeys[] = $__assetKey;

            // Check if we're in a Livewire component or not and store the asset accordingly...
            if (isset($this)) {
                \Livewire\store($this)->push('assets', $__output, $__assetKey);
            } else {
                \Livewire\Features\SupportScriptsAndAssets\SupportScriptsAndAssets::$nonLivewireAssets[$__assetKey] = $__output;
            }
        }
    ?>

    
        <?php
        $__scriptKey = '3265081075-1';
        ob_start();
    ?>
    <script>
        
        const mobileSize = window.matchMedia("(max-width: 480px)");

        let data = Alpine.reactive({windowWidth: 'max-w-2xl'});

        // Add a media query change listener
        mobileSize.addEventListener('change', (e) => {
            data.windowWidth = e.matches ? 'screen' : 'max-w-2xl';
        });

        Alpine.effect(() => {
            $wire.$set('modalWidth', data.windowWidth);
        });

        
        $wire.on('initializeLink', token => {
            const handler = Plaid.create({
                token: token,
                onSuccess: (publicToken, metadata) => {
                    $wire.dispatchSelf('linkSuccess', {publicToken: publicToken, metadata: metadata});
                },
                onExit: (err, metadata) => {
                },
                onEvent: (eventName, metadata) => {
                },
            });

            handler.open();
        });
    </script>
        <?php
        $__output = ob_get_clean();

        \Livewire\store($this)->push('scripts', $__output, $__scriptKey)
    ?>
</div>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\livewire\company\service\connected-account\list-institutions.blade.php ENDPATH**/ ?>