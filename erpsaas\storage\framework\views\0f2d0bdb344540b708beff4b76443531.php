<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'categoryHeaders',
    'alignmentClass' => null,
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'categoryHeaders',
    'alignmentClass' => null,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>


<tr class="bg-gray-50 dark:bg-white/5">
    <?php $__currentLoopData = $categoryHeaders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $header): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <th
            class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                'px-3 py-3.5 sm:first-of-type:ps-6 sm:last-of-type:pe-6',
                $alignmentClass($index) => $alignmentClass,
            ]); ?>"
        >
            <span class="text-sm font-semibold leading-6 text-gray-950 dark:text-white">
                <?php echo e($header); ?>

            </span>
        </th>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</tr>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\company\tables\category-header.blade.php ENDPATH**/ ?>