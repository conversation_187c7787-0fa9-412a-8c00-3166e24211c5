<?php if (isset($component)) { $__componentOriginal511d4862ff04963c3c16115c05a86a9d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal511d4862ff04963c3c16115c05a86a9d = $attributes; } ?>
<?php $component = Illuminate\View\DynamicComponent::resolve(['component' => $getFieldWrapperView()] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dynamic-component'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\DynamicComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['field' => $field]); ?>
    <?php if (isset($component)) { $__componentOriginal0c1a4ebbe2fce0f56bc1979d247d54ad = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0c1a4ebbe2fce0f56bc1979d247d54ad = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-simple-alert::components.simple-alert','data' => ['icon' => $getIcon(),'iconVerticalAlignment' => $getIconVerticalAlignment(),'iconAnimation' => $getIconAnimation(),'color' => $getColor(),'title' => $getTitle(),'description' => $getDescription(),'link' => $getLink(),'linkLabel' => $getLinkLabel(),'linkBlank' => $getLinkBlank(),'actionsVerticalAlignment' => $getActionsVerticalAlignment(),'actions' => $getActions(),'border' => $getBorder()]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-simple-alert::simple-alert'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($getIcon()),'icon-vertical-alignment' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($getIconVerticalAlignment()),'icon-animation' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($getIconAnimation()),'color' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($getColor()),'title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($getTitle()),'description' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($getDescription()),'link' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($getLink()),'link-label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($getLinkLabel()),'link-blank' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($getLinkBlank()),'actions-vertical-alignment' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($getActionsVerticalAlignment()),'actions' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($getActions()),'border' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($getBorder())]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0c1a4ebbe2fce0f56bc1979d247d54ad)): ?>
<?php $attributes = $__attributesOriginal0c1a4ebbe2fce0f56bc1979d247d54ad; ?>
<?php unset($__attributesOriginal0c1a4ebbe2fce0f56bc1979d247d54ad); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0c1a4ebbe2fce0f56bc1979d247d54ad)): ?>
<?php $component = $__componentOriginal0c1a4ebbe2fce0f56bc1979d247d54ad; ?>
<?php unset($__componentOriginal0c1a4ebbe2fce0f56bc1979d247d54ad); ?>
<?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal511d4862ff04963c3c16115c05a86a9d)): ?>
<?php $attributes = $__attributesOriginal511d4862ff04963c3c16115c05a86a9d; ?>
<?php unset($__attributesOriginal511d4862ff04963c3c16115c05a86a9d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal511d4862ff04963c3c16115c05a86a9d)): ?>
<?php $component = $__componentOriginal511d4862ff04963c3c16115c05a86a9d; ?>
<?php unset($__componentOriginal511d4862ff04963c3c16115c05a86a9d); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\vendor\codewithdennis\filament-simple-alert\resources\views\components\simple-alert-field.blade.php ENDPATH**/ ?>