<?php

namespace App\Models\ArenaDoviz;

use App\Concerns\Blamable;
use App\Concerns\CompanyOwned;
use App\Enums\ArenaDoviz\ExchangeType;
use App\Models\Common\Client;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class CommissionRule extends Model
{
    use Blamable;
    use CompanyOwned;
    use HasFactory;
    use SoftDeletes;

    protected $table = 'commission_rules';

    protected $fillable = [
        'company_id',
        'client_id',
        'location_id',
        'rule_name',
        'exchange_type',
        'from_currency_code',
        'to_currency_code',
        'commission_type',
        'commission_rate',
        'fixed_amount',
        'min_commission',
        'max_commission',
        'calculation_method',
        'commission_currency',
        'tier_rules',
        'is_active',
        'valid_from',
        'valid_until',
        'priority',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'exchange_type' => ExchangeType::class,
        'commission_rate' => 'decimal:4',
        'fixed_amount' => 'decimal:2',
        'min_commission' => 'decimal:2',
        'max_commission' => 'decimal:2',
        'tier_rules' => 'array',
        'is_active' => 'boolean',
        'valid_from' => 'date',
        'valid_until' => 'date',
        'priority' => 'integer',
    ];

    /**
     * Commission types
     */
    public const COMMISSION_TYPES = [
        'percentage' => 'Percentage',
        'fixed' => 'Fixed Amount',
        'tiered' => 'Tiered',
        'hybrid' => 'Hybrid (Percentage + Fixed)',
    ];

    /**
     * Calculation methods
     */
    public const CALCULATION_METHODS = [
        'pre_conversion' => 'Pre-Conversion (Source Amount)',
        'post_conversion' => 'Post-Conversion (Destination Amount)',
        'higher_amount' => 'Higher of Source/Destination',
        'lower_amount' => 'Lower of Source/Destination',
    ];

    /**
     * Get the client this rule applies to
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Get the location this rule applies to
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * Calculate commission for a given exchange
     */
    public function calculateCommission(float $sourceAmount, float $destinationAmount, string $sourceCurrency, string $destinationCurrency): array
    {
        $baseAmount = $this->getBaseAmount($sourceAmount, $destinationAmount);
        $commissionAmount = 0;
        $commissionCurrency = $this->commission_currency ?: $destinationCurrency;

        switch ($this->commission_type) {
            case 'percentage':
                $commissionAmount = $baseAmount * ($this->commission_rate / 100);
                break;

            case 'fixed':
                $commissionAmount = $this->fixed_amount;
                break;

            case 'tiered':
                $commissionAmount = $this->calculateTieredCommission($baseAmount);
                break;

            case 'hybrid':
                $percentageCommission = $baseAmount * ($this->commission_rate / 100);
                $commissionAmount = $percentageCommission + $this->fixed_amount;
                break;
        }

        // Apply min/max limits
        if ($this->min_commission && $commissionAmount < $this->min_commission) {
            $commissionAmount = $this->min_commission;
        }

        if ($this->max_commission && $commissionAmount > $this->max_commission) {
            $commissionAmount = $this->max_commission;
        }

        return [
            'commission_amount' => round($commissionAmount, 2),
            'commission_currency' => $commissionCurrency,
            'commission_rate_used' => $this->commission_rate,
            'base_amount' => $baseAmount,
            'calculation_method' => $this->calculation_method,
            'rule_applied' => $this->rule_name,
        ];
    }

    /**
     * Get base amount for commission calculation
     */
    private function getBaseAmount(float $sourceAmount, float $destinationAmount): float
    {
        return match ($this->calculation_method) {
            'pre_conversion' => $sourceAmount,
            'post_conversion' => $destinationAmount,
            'higher_amount' => max($sourceAmount, $destinationAmount),
            'lower_amount' => min($sourceAmount, $destinationAmount),
            default => $destinationAmount,
        };
    }

    /**
     * Calculate tiered commission based on amount ranges
     */
    private function calculateTieredCommission(float $amount): float
    {
        if (!$this->tier_rules || !is_array($this->tier_rules)) {
            return 0;
        }

        $commission = 0;
        $remainingAmount = $amount;

        foreach ($this->tier_rules as $tier) {
            $tierMin = $tier['min_amount'] ?? 0;
            $tierMax = $tier['max_amount'] ?? PHP_FLOAT_MAX;
            $tierRate = $tier['rate'] ?? 0;

            if ($remainingAmount <= 0) {
                break;
            }

            $tierAmount = min($remainingAmount, $tierMax - $tierMin);
            if ($tierAmount > 0) {
                $commission += $tierAmount * ($tierRate / 100);
                $remainingAmount -= $tierAmount;
            }
        }

        return $commission;
    }

    /**
     * Check if rule is currently valid
     */
    public function isValid(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $now = now()->toDateString();

        if ($this->valid_from && $now < $this->valid_from->toDateString()) {
            return false;
        }

        if ($this->valid_until && $now > $this->valid_until->toDateString()) {
            return false;
        }

        return true;
    }

    /**
     * Check if rule matches exchange criteria
     */
    public function matches(array $criteria): bool
    {
        // Check client
        if ($this->client_id && $this->client_id !== ($criteria['client_id'] ?? null)) {
            return false;
        }

        // Check location
        if ($this->location_id && $this->location_id !== ($criteria['location_id'] ?? null)) {
            return false;
        }

        // Check exchange type
        if ($this->exchange_type && $this->exchange_type->value !== ($criteria['exchange_type'] ?? null)) {
            return false;
        }

        // Check currencies
        if ($this->from_currency_code && $this->from_currency_code !== ($criteria['from_currency_code'] ?? null)) {
            return false;
        }

        if ($this->to_currency_code && $this->to_currency_code !== ($criteria['to_currency_code'] ?? null)) {
            return false;
        }

        return true;
    }

    /**
     * Get formatted commission rate
     */
    public function getFormattedRateAttribute(): string
    {
        $currency = $this->commission_currency ?? 'TRY'; // Default to TRY if null

        return match ($this->commission_type) {
            'percentage' => $this->commission_rate . '%',
            'fixed' => \App\Helpers\ArenaDoviz\NumberFormatter::formatCurrency($this->fixed_amount, $currency),
            'tiered' => 'Tiered rates',
            'hybrid' => $this->commission_rate . '% + ' . \App\Helpers\ArenaDoviz\NumberFormatter::formatCurrency($this->fixed_amount, $currency),
            default => 'N/A',
        };
    }

    /**
     * Scope for active rules
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for valid rules (within date range)
     */
    public function scopeValid($query)
    {
        $now = now()->toDateString();
        
        return $query->where('is_active', true)
            ->where(function ($q) use ($now) {
                $q->whereNull('valid_from')
                  ->orWhere('valid_from', '<=', $now);
            })
            ->where(function ($q) use ($now) {
                $q->whereNull('valid_until')
                  ->orWhere('valid_until', '>=', $now);
            });
    }

    /**
     * Scope for rules by priority
     */
    public function scopeByPriority($query)
    {
        return $query->orderBy('priority', 'desc');
    }
}
