<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'icon' => null,
    'currentTenant' => null,
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'icon' => null,
    'currentTenant' => null,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<?php
    $currentTenantName = filament()->getTenantName($currentTenant);
    $currentCompany = auth()->user()->currentCompany;
    $currentCompanyOwner = $currentCompany->owner;
    $items = filament()->getTenantMenuItems();

    $profileItem = $items['profile'] ?? null;
    $profileItemUrl = $profileItem?->getUrl();

    $registrationItem = $items['register'] ?? null;
    $registrationItemUrl = $registrationItem?->getUrl();
    $isRegistrationItemVisible = $registrationItem?->isVisible() ?? true;
    $hasRegistrationItem = ((filament()->hasTenantRegistration() && filament()->getTenantRegistrationPage()::canView()) || filled($registrationItemUrl)) && $isRegistrationItemVisible;

    $canSwitchTenants = count($tenants = array_filter(
        filament()->getUserTenants(filament()->auth()->user()),
        fn (\Illuminate\Database\Eloquent\Model $tenant): bool => ! $tenant->is($currentTenant),
    ));
?>


<?php if($currentTenant): ?>
    <?php if (isset($component)) { $__componentOriginal7ebda8c6f587ceec1ee02d1b7f0111a1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7ebda8c6f587ceec1ee02d1b7f0111a1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.panel-shift-dropdown.selected-tenant','data' => ['icon' => 'heroicon-m-check','iconColor' => 'primary','url' => filament()->getUrl($currentTenant),'label' => $currentTenantName]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('panel-shift-dropdown.selected-tenant'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'heroicon-m-check','icon-color' => 'primary','url' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(filament()->getUrl($currentTenant)),'label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentTenantName)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7ebda8c6f587ceec1ee02d1b7f0111a1)): ?>
<?php $attributes = $__attributesOriginal7ebda8c6f587ceec1ee02d1b7f0111a1; ?>
<?php unset($__attributesOriginal7ebda8c6f587ceec1ee02d1b7f0111a1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7ebda8c6f587ceec1ee02d1b7f0111a1)): ?>
<?php $component = $__componentOriginal7ebda8c6f587ceec1ee02d1b7f0111a1; ?>
<?php unset($__componentOriginal7ebda8c6f587ceec1ee02d1b7f0111a1); ?>
<?php endif; ?>
<?php endif; ?>
<?php if($canSwitchTenants): ?>
    <?php $__currentLoopData = $tenants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tenant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if (isset($component)) { $__componentOriginal2677d6ad3067c4282b32a41c21dedc6d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.panel-shift-dropdown.item','data' => ['url' => filament()->getUrl($tenant),'label' => filament()->getTenantName($tenant)]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('panel-shift-dropdown.item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['url' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(filament()->getUrl($tenant)),'label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(filament()->getTenantName($tenant))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d)): ?>
<?php $attributes = $__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d; ?>
<?php unset($__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2677d6ad3067c4282b32a41c21dedc6d)): ?>
<?php $component = $__componentOriginal2677d6ad3067c4282b32a41c21dedc6d; ?>
<?php unset($__componentOriginal2677d6ad3067c4282b32a41c21dedc6d); ?>
<?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php endif; ?>
<?php if($hasRegistrationItem): ?>
    <?php if (isset($component)) { $__componentOriginal2677d6ad3067c4282b32a41c21dedc6d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.panel-shift-dropdown.item','data' => ['url' => $registrationItemUrl ?? filament()->getTenantRegistrationUrl(),'label' => $registrationItem?->getLabel() ?? filament()->getTenantRegistrationPage()::getLabel(),'icon' => $registrationItem?->getIcon() ?? \Filament\Support\Facades\FilamentIcon::resolve('panels::tenant-menu.registration-button') ?? 'heroicon-m-plus']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('panel-shift-dropdown.item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['url' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($registrationItemUrl ?? filament()->getTenantRegistrationUrl()),'label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($registrationItem?->getLabel() ?? filament()->getTenantRegistrationPage()::getLabel()),'icon' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($registrationItem?->getIcon() ?? \Filament\Support\Facades\FilamentIcon::resolve('panels::tenant-menu.registration-button') ?? 'heroicon-m-plus')]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d)): ?>
<?php $attributes = $__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d; ?>
<?php unset($__attributesOriginal2677d6ad3067c4282b32a41c21dedc6d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2677d6ad3067c4282b32a41c21dedc6d)): ?>
<?php $component = $__componentOriginal2677d6ad3067c4282b32a41c21dedc6d; ?>
<?php unset($__componentOriginal2677d6ad3067c4282b32a41c21dedc6d); ?>
<?php endif; ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\panel-shift-dropdown\company-switcher.blade.php ENDPATH**/ ?>