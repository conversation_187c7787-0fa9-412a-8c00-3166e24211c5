<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
        <!--[if BLOCK]><![endif]--><?php if(method_exists($this, 'filtersForm')): ?>
            <?php echo e($this->filtersForm); ?>

        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>

    <?php if (isset($component)) { $__componentOriginaldce80491e51265a9a2a3daacfd07df81 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaldce80491e51265a9a2a3daacfd07df81 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.tables.container','data' => ['reportLoaded' => $this->reportLoaded]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.tables.container'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['report-loaded' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->reportLoaded)]); ?>
        <!--[if BLOCK]><![endif]--><?php if(! $this->tableHasEmptyState()): ?>
            <?php if (isset($component)) { $__componentOriginala16c2a03baf37b071543d3da6e81f5fe = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala16c2a03baf37b071543d3da6e81f5fe = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.tables.reports.account-transactions','data' => ['report' => $this->report]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.tables.reports.account-transactions'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['report' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->report)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala16c2a03baf37b071543d3da6e81f5fe)): ?>
<?php $attributes = $__attributesOriginala16c2a03baf37b071543d3da6e81f5fe; ?>
<?php unset($__attributesOriginala16c2a03baf37b071543d3da6e81f5fe); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala16c2a03baf37b071543d3da6e81f5fe)): ?>
<?php $component = $__componentOriginala16c2a03baf37b071543d3da6e81f5fe; ?>
<?php unset($__componentOriginala16c2a03baf37b071543d3da6e81f5fe); ?>
<?php endif; ?>
        <?php else: ?>
            <?php if (isset($component)) { $__componentOriginal9608358ee6650b8d3d7c633eba6d028e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9608358ee6650b8d3d7c633eba6d028e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-tables::components.empty-state.index','data' => ['actions' => $this->getEmptyStateActions(),'description' => $this->getEmptyStateDescription(),'heading' => $this->getEmptyStateHeading(),'icon' => $this->getEmptyStateIcon()]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-tables::empty-state'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['actions' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->getEmptyStateActions()),'description' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->getEmptyStateDescription()),'heading' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->getEmptyStateHeading()),'icon' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->getEmptyStateIcon())]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9608358ee6650b8d3d7c633eba6d028e)): ?>
<?php $attributes = $__attributesOriginal9608358ee6650b8d3d7c633eba6d028e; ?>
<?php unset($__attributesOriginal9608358ee6650b8d3d7c633eba6d028e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9608358ee6650b8d3d7c633eba6d028e)): ?>
<?php $component = $__componentOriginal9608358ee6650b8d3d7c633eba6d028e; ?>
<?php unset($__componentOriginal9608358ee6650b8d3d7c633eba6d028e); ?>
<?php endif; ?>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaldce80491e51265a9a2a3daacfd07df81)): ?>
<?php $attributes = $__attributesOriginaldce80491e51265a9a2a3daacfd07df81; ?>
<?php unset($__attributesOriginaldce80491e51265a9a2a3daacfd07df81); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaldce80491e51265a9a2a3daacfd07df81)): ?>
<?php $component = $__componentOriginaldce80491e51265a9a2a3daacfd07df81; ?>
<?php unset($__componentOriginaldce80491e51265a9a2a3daacfd07df81); ?>
<?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views/filament/company/pages/reports/account-transactions.blade.php ENDPATH**/ ?>