<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'icon' => null,
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'icon' => null,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<li class="grid grid-flow-col auto-cols-auto gap-x-2 items-start p-2">
    <div class="icon h-9 w-9 flex items-center justify-center rounded-full bg-gray-200 dark:bg-white/10">
        <?php if (isset($component)) { $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.icon','data' => ['icon' => $icon,'class' => 'h-6 w-6 text-gray-600 dark:text-gray-200']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($icon),'class' => 'h-6 w-6 text-gray-600 dark:text-gray-200']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $attributes = $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $component = $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
    </div>
    <div>
        <div class="px-2 pb-2">
            <h2 class="text-gray-800 dark:text-gray-200 text-base font-semibold">
                <?php echo e(__('Dark mode')); ?>

            </h2>
            <p class="text-sm font-normal text-gray-500 dark:text-gray-400">
                <?php echo e(__('Adjust the appearance to reduce glare and give your eyes a break.')); ?>

            </p>
        </div>
        <!-- Custom radio buttons for Theme setting -->
        <template x-for="(label, value) in themeLabels" :key="value">
            <div class="cursor-pointer p-2 rounded-lg hover:bg-gray-50 focus-visible:bg-gray-50 dark:hover:bg-white/5 dark:focus-visible:bg-white/5" x-on:click="setTheme(value)">
                <label class="text-sm font-medium flex items-center justify-between cursor-pointer">
                    <span x-text="label"></span>
                    <input type="radio" class="sr-only" :id="'theme' + value" :name="'theme'" :value="value" x-model="theme">
                    <span
                        class="h-3 w-3 ring-2 rounded-full"
                        x-bind:class="{
                            'border-2 border-white dark:border-gray-800 bg-primary-500 dark:bg-primary-400 ring-primary-500 dark:ring-primary-400': theme === value,
                            'ring-gray-400 dark:ring-gray-600': theme !== value
                        }"
                    ></span>
                </label>
            </div>
        </template>
    </div>
</li>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\panel-shift-dropdown\display-accessibility.blade.php ENDPATH**/ ?>