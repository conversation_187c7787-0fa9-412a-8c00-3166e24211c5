/**
 * Livewire Error Handler
 * Handles common Livewire errors gracefully
 */

document.addEventListener('DOMContentLoaded', function() {
    // Wait for Livewire to be available
    if (typeof window.Livewire !== 'undefined') {
        initializeLivewireErrorHandling();
    } else {
        // Wait for Livewire to load
        document.addEventListener('livewire:init', initializeLivewireErrorHandling);
    }
});

function initializeLivewireErrorHandling() {
    // Handle Livewire request failures
    window.Livewire.hook('request', ({ fail }) => {
        fail(({ status, content, preventDefault }) => {
            console.warn(`Livewire request failed with status: ${status}`);
            
            // Handle specific error codes
            switch (status) {
                case 419:
                    console.warn('CSRF token mismatch - page may need refresh');
                    // Let Livewire handle this naturally
                    break;
                    
                case 500:
                    console.error('Server error occurred:', content);
                    // Prevent default error modal for 500 errors
                    preventDefault();
                    
                    // Show a user-friendly message
                    showUserFriendlyError('A server error occurred. Please try again.');
                    break;
                    
                case 422:
                    console.warn('Validation error occurred');
                    // Let Livewire handle validation errors naturally
                    break;
                    
                default:
                    console.error(`Unexpected error (${status}):`, content);
            }
        });
    });
    
    // Handle component initialization errors
    window.Livewire.hook('component.init', ({ component }) => {
        try {
            // Additional component initialization logic if needed
        } catch (error) {
            console.error('Component initialization error:', error);
        }
    });
}

function showUserFriendlyError(message) {
    // Create a simple notification
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded shadow-lg z-50';
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

// Handle MutationObserver errors specifically
const originalMutationObserver = window.MutationObserver;
window.MutationObserver = function(callback) {
    const wrappedCallback = function(mutations, observer) {
        try {
            return callback.call(this, mutations, observer);
        } catch (error) {
            console.warn('MutationObserver callback error handled:', error.message);
            // Don't re-throw the error to prevent it from bubbling up
        }
    };
    
    return new originalMutationObserver(wrappedCallback);
};

// Ensure MutationObserver.observe is called with valid nodes
const originalObserve = originalMutationObserver.prototype.observe;
originalMutationObserver.prototype.observe = function(target, options) {
    try {
        // Check if target is a valid Node
        if (!target || typeof target.nodeType === 'undefined') {
            console.warn('MutationObserver.observe called with invalid target:', target);
            return;
        }
        
        return originalObserve.call(this, target, options);
    } catch (error) {
        console.warn('MutationObserver.observe error handled:', error.message);
    }
};
