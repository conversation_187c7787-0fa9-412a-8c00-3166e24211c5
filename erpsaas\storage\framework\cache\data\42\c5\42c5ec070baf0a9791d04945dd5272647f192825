1756292125O:58:"App\Transformers\EntityPaymentPerformanceReportTransformer":2:{s:9:" * report";O:17:"App\DTO\ReportDTO":10:{s:10:"categories";a:1:{s:8:"Entities";a:0:{}}s:12:"overallTotal";N;s:12:"agingSummary";N;s:18:"entityBalanceTotal";N;s:21:"overallPaymentMetrics";O:25:"App\DTO\PaymentMetricsDTO":6:{s:14:"totalDocuments";i:0;s:11:"onTimeCount";i:0;s:9:"lateCount";i:0;s:12:"avgDaysToPay";i:0;s:11:"avgDaysLate";i:0;s:17:"onTimePaymentRate";s:2:"0%";}s:6:"fields";a:7:{i:0;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:0;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";E:37:"Filament\Support\Enums\Alignment:Left";s:8:" * label";s:6:"Vendor";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:11:"entity_name";}i:1;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:0;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";E:38:"Filament\Support\Enums\Alignment:Right";s:8:" * label";s:11:"Total Bills";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:15:"total_documents";}i:2;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:1;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";r:36;s:8:" * label";s:12:"Paid On Time";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:13:"on_time_count";}i:3;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:1;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";r:36;s:8:" * label";s:9:"Paid Late";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:10:"late_count";}i:4;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:1;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";r:36;s:8:" * label";s:16:"Avg. Days to Pay";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:15:"avg_days_to_pay";}i:5;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:1;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";r:36;s:8:" * label";s:14:"Avg. Days Late";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:13:"avg_days_late";}i:6;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:0;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";r:36;s:8:" * label";s:12:"On Time Rate";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:20:"on_time_payment_rate";}}s:10:"reportType";N;s:8:"overview";N;s:9:"startDate";O:25:"Illuminate\Support\Carbon":3:{s:4:"date";s:26:"2025-01-01 00:00:00.000000";s:13:"timezone_type";i:3;s:8:"timezone";s:3:"UTC";}s:7:"endDate";O:25:"Illuminate\Support\Carbon":3:{s:4:"date";s:26:"2025-08-27 23:59:59.000000";s:13:"timezone_type";i:3;s:8:"timezone";s:3:"UTC";}}s:70:" App\Transformers\EntityPaymentPerformanceReportTransformer entityType";E:46:"App\Enums\Accounting\DocumentEntityType:Vendor";}