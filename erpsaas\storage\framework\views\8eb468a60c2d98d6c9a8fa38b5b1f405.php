<div class="space-y-6">
    <!-- Commission Overview -->
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('heading', null, []); ?> 
            <div class="flex items-center gap-2">
                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-banknotes'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                Commission Analysis Report
            </div>
         <?php $__env->endSlot(); ?>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg text-center">
                <div class="text-green-600 dark:text-green-400 text-sm font-medium">Total Commission</div>
                <div class="text-2xl font-bold text-green-900 dark:text-green-100">
                    <?php echo e(number_format($data['total_commission'], 2)); ?> TRY
                </div>
            </div>
            
            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg text-center">
                <div class="text-blue-600 dark:text-blue-400 text-sm font-medium">Avg Commission Rate</div>
                <div class="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    <?php echo e(number_format($data['avg_commission_rate'], 2)); ?>%
                </div>
            </div>
            
            <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg text-center">
                <div class="text-yellow-600 dark:text-yellow-400 text-sm font-medium">Min Commission Rate</div>
                <div class="text-2xl font-bold text-yellow-900 dark:text-yellow-100">
                    <?php echo e(number_format($data['min_commission_rate'], 2)); ?>%
                </div>
            </div>
            
            <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg text-center">
                <div class="text-purple-600 dark:text-purple-400 text-sm font-medium">Max Commission Rate</div>
                <div class="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    <?php echo e(number_format($data['max_commission_rate'], 2)); ?>%
                </div>
            </div>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>

    <!-- Commission by Transaction Type -->
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('heading', null, []); ?> Commission by Transaction Type <?php $__env->endSlot(); ?>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-800">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Transaction Type
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Total Commission (TRY)
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Avg Rate (%)
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Transaction Count
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Avg Commission per Transaction
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            % of Total Commission
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    <?php $__currentLoopData = $data['commission_by_type']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $stats): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100 capitalize">
                                <?php echo e(str_replace('_', ' ', $type)); ?>

                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400 font-semibold">
                                <?php echo e(number_format($stats['total_commission'], 2)); ?>

                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 dark:text-blue-400 font-semibold">
                                <?php echo e(number_format($stats['avg_rate'], 2)); ?>%
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                <?php echo e(number_format($stats['transaction_count'])); ?>

                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                <?php echo e($stats['transaction_count'] > 0 ? number_format($stats['total_commission'] / $stats['transaction_count'], 2) : '0.00'); ?> TRY
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                <?php echo e($data['total_commission'] > 0 ? number_format(($stats['total_commission'] / $data['total_commission']) * 100, 1) : '0.0'); ?>%
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>

    <!-- Commission Rate Distribution -->
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('heading', null, []); ?> Commission Rate Distribution <?php $__env->endSlot(); ?>
        
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <?php $__currentLoopData = $data['commission_distribution']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $range => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php
                    $colors = [
                        '0-0.5%' => 'red',
                        '0.5-1%' => 'yellow',
                        '1-1.5%' => 'blue',
                        '1.5-2%' => 'green',
                        '2%+' => 'purple',
                    ];
                    $color = $colors[$range] ?? 'gray';
                ?>
                
                <div class="bg-<?php echo e($color); ?>-50 dark:bg-<?php echo e($color); ?>-900/20 p-4 rounded-lg text-center">
                    <div class="text-<?php echo e($color); ?>-600 dark:text-<?php echo e($color); ?>-400 text-sm font-medium">
                        <?php echo e($range); ?>

                    </div>
                    <div class="text-2xl font-bold text-<?php echo e($color); ?>-900 dark:text-<?php echo e($color); ?>-100">
                        <?php echo e(number_format($count)); ?>

                    </div>
                    <div class="text-xs text-<?php echo e($color); ?>-600 dark:text-<?php echo e($color); ?>-400 mt-1">
                        transactions
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        
        <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <h4 class="font-semibold text-gray-900 dark:text-gray-100 mb-3">Distribution Analysis</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <?php
                        $totalTransactions = array_sum($data['commission_distribution']);
                        $highRateTransactions = ($data['commission_distribution']['1.5-2%'] ?? 0) + ($data['commission_distribution']['2%+'] ?? 0);
                        $lowRateTransactions = ($data['commission_distribution']['0-0.5%'] ?? 0) + ($data['commission_distribution']['0.5-1%'] ?? 0);
                    ?>
                    
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-sm">High Rate Transactions (1.5%+)</span>
                            <span class="text-sm font-semibold text-green-600">
                                <?php echo e(number_format($highRateTransactions)); ?> 
                                (<?php echo e($totalTransactions > 0 ? number_format(($highRateTransactions / $totalTransactions) * 100, 1) : '0.0'); ?>%)
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm">Low Rate Transactions (<1%)</span>
                            <span class="text-sm font-semibold text-red-600">
                                <?php echo e(number_format($lowRateTransactions)); ?> 
                                (<?php echo e($totalTransactions > 0 ? number_format(($lowRateTransactions / $totalTransactions) * 100, 1) : '0.0'); ?>%)
                            </span>
                        </div>
                    </div>
                </div>
                
                <div>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-sm">Most Common Range</span>
                            <span class="text-sm font-semibold text-blue-600">
                                <?php
                                    $maxRange = array_keys($data['commission_distribution'], max($data['commission_distribution']))[0];
                                ?>
                                <?php echo e($maxRange); ?>

                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm">Rate Spread</span>
                            <span class="text-sm font-semibold text-purple-600">
                                <?php echo e(number_format($data['max_commission_rate'] - $data['min_commission_rate'], 2)); ?>%
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>

    <!-- Performance Insights -->
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('heading', null, []); ?> Performance Insights <?php $__env->endSlot(); ?>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
                <h4 class="font-semibold text-gray-900 dark:text-gray-100">Commission Efficiency</h4>
                
                <?php
                    $bestPerformingType = collect($data['commission_by_type'])->sortByDesc('avg_rate')->first();
                    $mostProfitableType = collect($data['commission_by_type'])->sortByDesc('total_commission')->first();
                ?>
                
                <div class="space-y-3">
                    <div class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <div class="text-green-800 dark:text-green-200 font-medium">Best Rate Performance</div>
                        <div class="text-green-600 dark:text-green-400 text-sm mt-1">
                            <?php if($bestPerformingType): ?>
                                <?php echo e(ucwords(str_replace('_', ' ', array_search($bestPerformingType, $data['commission_by_type']->toArray())))); ?> 
                                - <?php echo e(number_format($bestPerformingType['avg_rate'], 2)); ?>% avg rate
                            <?php else: ?>
                                No data available
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <div class="text-blue-800 dark:text-blue-200 font-medium">Most Profitable Type</div>
                        <div class="text-blue-600 dark:text-blue-400 text-sm mt-1">
                            <?php if($mostProfitableType): ?>
                                <?php echo e(ucwords(str_replace('_', ' ', array_search($mostProfitableType, $data['commission_by_type']->toArray())))); ?> 
                                - <?php echo e(number_format($mostProfitableType['total_commission'], 2)); ?> TRY total
                            <?php else: ?>
                                No data available
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="space-y-4">
                <h4 class="font-semibold text-gray-900 dark:text-gray-100">Optimization Opportunities</h4>
                
                <div class="space-y-3">
                    <?php if($lowRateTransactions > 0): ?>
                        <div class="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                            <div class="text-yellow-800 dark:text-yellow-200 font-medium">Low Rate Alert</div>
                            <div class="text-yellow-600 dark:text-yellow-400 text-sm mt-1">
                                <?php echo e(number_format($lowRateTransactions)); ?> transactions have rates below 1%. 
                                Consider reviewing pricing strategy.
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php
                        $rateVariance = $data['max_commission_rate'] - $data['min_commission_rate'];
                    ?>
                    
                    <?php if($rateVariance > 3): ?>
                        <div class="p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                            <div class="text-orange-800 dark:text-orange-200 font-medium">Rate Consistency</div>
                            <div class="text-orange-600 dark:text-orange-400 text-sm mt-1">
                                Large rate spread (<?php echo e(number_format($rateVariance, 2)); ?>%). 
                                Consider standardizing commission rates.
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <div class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <div class="text-green-800 dark:text-green-200 font-medium">Revenue Potential</div>
                        <div class="text-green-600 dark:text-green-400 text-sm mt-1">
                            If all transactions achieved <?php echo e(number_format($data['avg_commission_rate'] + 0.5, 2)); ?>% rate, 
                            potential additional revenue could be significant.
                        </div>
                    </div>
                </div>
            </div>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>

    <!-- Summary Statistics -->
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('heading', null, []); ?> Summary Statistics <?php $__env->endSlot(); ?>
        
        <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
                <div>
                    <div class="text-2xl font-bold text-green-600">
                        <?php echo e(number_format($data['total_commission'], 0)); ?> TRY
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Total Commission Earned</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-blue-600">
                        <?php echo e(number_format($data['avg_commission_rate'], 2)); ?>%
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Average Commission Rate</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-purple-600">
                        <?php echo e(number_format(array_sum($data['commission_distribution']))); ?>

                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Total Transactions Analyzed</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-orange-600">
                        <?php echo e(number_format($rateVariance, 2)); ?>%
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Rate Spread (Max - Min)</div>
                </div>
            </div>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
</div>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\filament\company\reports\commission-analysis.blade.php ENDPATH**/ ?>