<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'text' => null,
    'icon' => null,
    'placement' => 'right',
    'maxWidth' => 300,
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'text' => null,
    'icon' => null,
    'placement' => 'right',
    'maxWidth' => 300,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<div class="es-tooltip" x-data="{ open: false }">
    <span
        x-ref="trigger"
        @click="open = !open"
        @click.away="open = false"
        x-tooltip="{
            content: () => $refs.tooltipContent ? $refs.tooltipContent.innerHTML : '',
            trigger: 'click',
            appendTo: $root,
            allowHTML: true,
            interactive: true,
            theme: $store.theme,
            placement: '<?php echo e($placement); ?>',
            maxWidth: <?php echo e($maxWidth); ?>,
            popperOptions: {
                strategy: 'fixed',
                modifiers: [
                    {
                        name: 'flip',
                        enabled: true,
                        options: {
                            fallbackPlacements: ['left', 'top', 'bottom'],
                        }
                    },
                    {
                        name: 'preventOverflow',
                        enabled: true,
                        options: {
                            boundary: 'viewport',
                            padding: 8
                        }
                    }
                ]
            }
        }">
        <?php if (isset($component)) { $__componentOriginalf0029cce6d19fd6d472097ff06a800a1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf0029cce6d19fd6d472097ff06a800a1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.icon-button','data' => ['icon' => $icon,'class' => 'w-5 h-5 text-gray-400 hover:text-primary-600 focus-visible:ring-primary-600 dark:text-gray-500 dark:hover:text-primary-300 dark:focus-visible:ring-primary-500']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::icon-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($icon),'class' => 'w-5 h-5 text-gray-400 hover:text-primary-600 focus-visible:ring-primary-600 dark:text-gray-500 dark:hover:text-primary-300 dark:focus-visible:ring-primary-500']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf0029cce6d19fd6d472097ff06a800a1)): ?>
<?php $attributes = $__attributesOriginalf0029cce6d19fd6d472097ff06a800a1; ?>
<?php unset($__attributesOriginalf0029cce6d19fd6d472097ff06a800a1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf0029cce6d19fd6d472097ff06a800a1)): ?>
<?php $component = $__componentOriginalf0029cce6d19fd6d472097ff06a800a1; ?>
<?php unset($__componentOriginalf0029cce6d19fd6d472097ff06a800a1); ?>
<?php endif; ?>
    </span>
    <template x-ref="tooltipContent">
        <div class="es-tooltip-content-wrapper py-4 px-5">
            <button @click="$refs.trigger.click()" class="es-close-tooltip"></button>
            <div class="es-tooltip-content">
                <p class="es-tooltip-text text-sm font-normal text-gray-800 dark:text-gray-200">
                    <?php echo e($text); ?>

                </p>
            </div>
        </div>
    </template>
</div>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\tooltip.blade.php ENDPATH**/ ?>