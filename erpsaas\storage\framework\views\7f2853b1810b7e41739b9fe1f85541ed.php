<!DOCTYPE html>
<html>
<head>
    <title>Invoice #<?php echo e($document->number); ?></title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Include Tailwind -->
    <script src="https://cdn.tailwindcss.com"></script>

    <?php echo $document->getFontHtml(); ?>


    <style>
        body {
            background-color: white;
            color: black;
        }

        .doc-template-paper {
            font-family: '<?php echo e($document->font->getLabel()); ?>', sans-serif;
        }

        @media print {
            body {
                print-color-adjust: exact !important;
                -webkit-print-color-adjust: exact !important;
                margin: 0;
                padding: 0;
            }

            @page {
                size: auto;
                margin: 7.5mm 0;
            }

            @page:first {
                margin-top: 0;
            }

            .doc-template-container {
                padding: 0 !important;
                margin: 0 !important;

                > div {
                    overflow: hidden !important;
                    max-height: none !important;
                    max-width: none !important;
                    box-shadow: none !important;
                    border-radius: 0 !important;
                }
            }

            .doc-template-paper {
                overflow: hidden !important;
                max-height: none !important;
                max-width: none !important;
                height: auto !important;
                width: auto !important;
            }

            .doc-template-line-items .summary-section {
                display: table-row-group;
                page-break-inside: avoid;
            }

            .doc-template-line-items tr {
                page-break-inside: avoid;
                page-break-after: auto;
            }

            .doc-template-footer {
                page-break-inside: avoid;
                page-break-before: auto;
            }
        }
    </style>
</head>
<body>
    <?php echo $__env->make("filament.company.components.document-templates.{$template->value}", [
        'document' => $document,
        'preview' => false,
    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\print-document.blade.php ENDPATH**/ ?>