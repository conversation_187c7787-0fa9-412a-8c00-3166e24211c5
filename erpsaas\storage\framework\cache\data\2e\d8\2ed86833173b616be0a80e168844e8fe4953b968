1756292114O:52:"App\Transformers\AccountTransactionReportTransformer":1:{s:9:" * report";O:17:"App\DTO\ReportDTO":10:{s:10:"categories";a:0:{}s:12:"overallTotal";N;s:12:"agingSummary";N;s:18:"entityBalanceTotal";N;s:21:"overallPaymentMetrics";N;s:6:"fields";a:5:{i:0;O:18:"App\Support\Column":11:{s:9:" * isDate";b:1;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:0;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";E:37:"Filament\Support\Enums\Alignment:Left";s:8:" * label";s:4:"DATE";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:4:"date";}i:1;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:0;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";r:17;s:8:" * label";s:11:"DESCRIPTION";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:11:"description";}i:2;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:0;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";E:38:"Filament\Support\Enums\Alignment:Right";s:8:" * label";s:5:"DEBIT";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:5:"debit";}i:3;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:0;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";r:41;s:8:" * label";s:6:"CREDIT";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:6:"credit";}i:4;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:0;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";r:41;s:8:" * label";s:15:"RUNNING BALANCE";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:7:"balance";}}s:10:"reportType";N;s:8:"overview";N;s:9:"startDate";N;s:7:"endDate";N;}}