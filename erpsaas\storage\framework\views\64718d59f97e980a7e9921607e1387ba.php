<?php $__env->startSection('content'); ?>
    <div class="header">
        <div class="title"><?php echo e($report->getTitle()); ?></div>
        <div class="company-name"><?php echo e($company->name); ?></div>
        <?php if($startDate && $endDate): ?>
            <div class="date-range">Date Range: <?php echo e($startDate); ?> to <?php echo e($endDate); ?></div>
        <?php else: ?>
            <div class="date-range">As of <?php echo e($endDate); ?></div>
        <?php endif; ?>
    </div>
    <table class="table-class">
        <colgroup>
            <?php if(array_key_exists('account_code', $report->getHeaders())): ?>
                <col span="1" style="width: 20%;">
                <col span="1" style="width: 55%;">
                <col span="1" style="width: 25%;">
            <?php else: ?>
                <col span="1" style="width: 65%;">
                <col span="1" style="width: 35%;">
            <?php endif; ?>
        </colgroup>
        <thead class="table-head">
        <tr>
            <?php $__currentLoopData = $report->getHeaders(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $header): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <th class="<?php echo e($report->getAlignmentClass($index)); ?>">
                    <?php echo e($header); ?>

                </th>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tr>
        </thead>
        <?php $__currentLoopData = $report->getCategories(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tbody>
            <tr class="category-header-row">
                <?php $__currentLoopData = $category->header; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $header): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <td class="<?php echo e($report->getAlignmentClass($index)); ?>">
                        <?php echo e($header); ?>

                    </td>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tr>
            <?php $__currentLoopData = $category->data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <?php $__currentLoopData = $account; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $cell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <td class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                            $report->getAlignmentClass($index),
                            'whitespace-normal' => $index === 'account_name',
                            'whitespace-nowrap' => $index !== 'account_name',
                        ]); ?>"
                        >
                            <?php if(is_array($cell) && isset($cell['name'])): ?>
                                <?php echo e($cell['name']); ?>

                            <?php else: ?>
                                <?php echo e($cell); ?>

                            <?php endif; ?>
                        </td>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            <!-- Category Types -->
            <?php $__currentLoopData = $category->types ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <!-- Type Header -->
                <tr class="type-header-row">
                    <?php $__currentLoopData = $type->header; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $header): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <td class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                            $report->getAlignmentClass($index),
                            'type-row-indent' => $index === 'account_name',
                        ]); ?>"
                        >
                            <?php echo e($header); ?>

                        </td>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tr>

                <!-- Type Data -->
                <?php $__currentLoopData = $type->data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $typeRow): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr class="type-data-row">
                        <?php $__currentLoopData = $typeRow; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $cell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <td class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                                $report->getAlignmentClass($index),
                                'whitespace-normal type-row-indent' => $index === 'account_name',
                                'whitespace-nowrap' => $index !== 'account_name',
                            ]); ?>"
                            >
                                <?php if(is_array($cell) && isset($cell['name'])): ?>
                                    <?php echo e($cell['name']); ?>

                                <?php else: ?>
                                    <?php echo e($cell); ?>

                                <?php endif; ?>
                            </td>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                <!-- Type Summary -->
                <tr class="type-summary-row">
                    <?php $__currentLoopData = $type->summary; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $cell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <td class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                            $report->getAlignmentClass($index),
                            'type-row-indent' => $index === 'account_name',
                        ]); ?>"
                        >
                            <?php echo e($cell); ?>

                        </td>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            <tr class="category-summary-row">
                <?php $__currentLoopData = $category->summary; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $cell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <td class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                        $report->getAlignmentClass($index),
                        'underline-bold' => $loop->last,
                    ]); ?>"
                    >
                        <?php echo e($cell); ?>

                    </td>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tr>

            <?php if (! ($loop->last && empty($report->getOverallTotals()))): ?>
                <tr class="spacer-row">
                    <td colspan="<?php echo e(count($report->getHeaders())); ?>"></td>
                </tr>
            <?php endif; ?>
            </tbody>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <tfoot>
        <tr class="table-footer-row">
            <?php $__currentLoopData = $report->getOverallTotals(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $total): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <td class="<?php echo e($report->getAlignmentClass($index)); ?>">
                    <?php echo e($total); ?>

                </td>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tr>
        </tfoot>
    </table>

    <!-- Second Overview Table -->
    <table class="table-class" style="margin-top: 40px;">
        <colgroup>
            <?php if(array_key_exists('account_code', $report->getHeaders())): ?>
                <col span="1" style="width: 20%;">
                <col span="1" style="width: 55%;">
                <col span="1" style="width: 25%;">
            <?php else: ?>
                <col span="1" style="width: 65%;">
                <col span="1" style="width: 35%;">
            <?php endif; ?>
        </colgroup>
        <thead class="table-head">
        <tr>
            <?php $__currentLoopData = $report->getOverviewHeaders(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $header): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <th class="<?php echo e($report->getAlignmentClass($index)); ?>">
                    <?php echo e($header); ?>

                </th>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tr>
        </thead>
        <!-- Overview Content -->
        <?php $__currentLoopData = $report->getOverview(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $overviewCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tbody>
            <tr class="category-header-row">
                <?php $__currentLoopData = $overviewCategory->header; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $header): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <td class="<?php echo e($report->getAlignmentClass($index)); ?>">
                        <?php echo e($header); ?>

                    </td>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tr>
            <?php $__currentLoopData = $overviewCategory->data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $overviewAccount): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <?php $__currentLoopData = $overviewAccount; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $cell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <td class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                            $report->getAlignmentClass($index),
                            'whitespace-normal' => $index === 'account_name',
                            'whitespace-nowrap' => $index !== 'account_name',
                        ]); ?>"
                        >
                            <?php if(is_array($cell) && isset($cell['name'])): ?>
                                <?php echo e($cell['name']); ?>

                            <?php else: ?>
                                <?php echo e($cell); ?>

                            <?php endif; ?>
                        </td>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <!-- Summary Row -->
            <tr class="category-summary-row">
                <?php $__currentLoopData = $overviewCategory->summary; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $summaryCell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <td class="<?php echo e($report->getAlignmentClass($index)); ?>">
                        <?php echo e($summaryCell); ?>

                    </td>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tr>

            <?php if($overviewCategory->header['account_name'] === 'Starting Balance'): ?>
                <?php $__currentLoopData = $report->getOverviewAlignedWithColumns(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $summaryRow): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <?php $__currentLoopData = $summaryRow; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $summaryCell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <td class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                                $report->getAlignmentClass($index),
                                'font-bold' => $loop->parent->last,
                                'underline-thin' => $loop->parent->remaining === 1 && $index === 'net_movement',
                                'underline-bold' => $loop->parent->last && $index === 'net_movement',
                            ]); ?>"
                            >
                                <?php echo e($summaryCell); ?>

                            </td>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>
            </tbody>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </table>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('components.company.reports.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\company\reports\cash-flow-statement-pdf.blade.php ENDPATH**/ ?>