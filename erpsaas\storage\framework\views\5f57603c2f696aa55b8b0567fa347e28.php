<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
        <div class="flex flex-col md:flex-row items-start md:items-center gap-4">
            <!-- Form Container -->
            <?php if(method_exists($this, 'filtersForm')): ?>
                <div class="flex-1 min-w-0">
                    <?php echo e($this->filtersForm); ?>

                </div>
            <?php endif; ?>

            <!-- Grouping Button and Column Toggle -->
            <?php if($this->hasToggleableColumns()): ?>
                <div class="flex-shrink-0 mr-4">
                    <?php if (isset($component)) { $__componentOriginal819b24f9f8a080df7cdd61b9f97a96fc = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal819b24f9f8a080df7cdd61b9f97a96fc = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-tables::components.column-toggle.dropdown','data' => ['form' => $this->getTableColumnToggleForm(),'triggerAction' => $this->getToggleColumnsTriggerAction()]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-tables::column-toggle.dropdown'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['form' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->getTableColumnToggleForm()),'trigger-action' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->getToggleColumnsTriggerAction())]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal819b24f9f8a080df7cdd61b9f97a96fc)): ?>
<?php $attributes = $__attributesOriginal819b24f9f8a080df7cdd61b9f97a96fc; ?>
<?php unset($__attributesOriginal819b24f9f8a080df7cdd61b9f97a96fc); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal819b24f9f8a080df7cdd61b9f97a96fc)): ?>
<?php $component = $__componentOriginal819b24f9f8a080df7cdd61b9f97a96fc; ?>
<?php unset($__componentOriginal819b24f9f8a080df7cdd61b9f97a96fc); ?>
<?php endif; ?>
                </div>
            <?php endif; ?>

            <div class="flex-shrink-0 w-[9.5rem] flex justify-end">
                <?php echo e($this->applyFiltersAction); ?>

            </div>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>

    <?php if (isset($component)) { $__componentOriginal13ac27004055ce845ca58f4d9f3452fa = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal13ac27004055ce845ca58f4d9f3452fa = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.report-summary-section','data' => ['reportLoaded' => $this->reportLoaded,'summaryData' => $this->report?->getSummary(),'targetLabel' => 'Net Cash Flow']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('report-summary-section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['report-loaded' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->reportLoaded),'summary-data' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->report?->getSummary()),'target-label' => 'Net Cash Flow']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal13ac27004055ce845ca58f4d9f3452fa)): ?>
<?php $attributes = $__attributesOriginal13ac27004055ce845ca58f4d9f3452fa; ?>
<?php unset($__attributesOriginal13ac27004055ce845ca58f4d9f3452fa); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal13ac27004055ce845ca58f4d9f3452fa)): ?>
<?php $component = $__componentOriginal13ac27004055ce845ca58f4d9f3452fa; ?>
<?php unset($__componentOriginal13ac27004055ce845ca58f4d9f3452fa); ?>
<?php endif; ?>

    <?php if (isset($component)) { $__componentOriginal176e152b2aa4f126a8cc5f6aae6baaf9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal176e152b2aa4f126a8cc5f6aae6baaf9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.report-tabs','data' => ['activeTab' => $activeTab,'tabs' => $this->getTabs()]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('report-tabs'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['active-tab' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($activeTab),'tabs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->getTabs())]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal176e152b2aa4f126a8cc5f6aae6baaf9)): ?>
<?php $attributes = $__attributesOriginal176e152b2aa4f126a8cc5f6aae6baaf9; ?>
<?php unset($__attributesOriginal176e152b2aa4f126a8cc5f6aae6baaf9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal176e152b2aa4f126a8cc5f6aae6baaf9)): ?>
<?php $component = $__componentOriginal176e152b2aa4f126a8cc5f6aae6baaf9; ?>
<?php unset($__componentOriginal176e152b2aa4f126a8cc5f6aae6baaf9); ?>
<?php endif; ?>

    <?php if (isset($component)) { $__componentOriginaldce80491e51265a9a2a3daacfd07df81 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaldce80491e51265a9a2a3daacfd07df81 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.tables.container','data' => ['reportLoaded' => $this->reportLoaded]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.tables.container'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['report-loaded' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->reportLoaded)]); ?>
        <?php if($this->report): ?>
            <?php if($activeTab === 'summary'): ?>
                <?php if (isset($component)) { $__componentOriginal604c611b10ad2303150a5b3f24357654 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal604c611b10ad2303150a5b3f24357654 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.tables.reports.cash-flow-statement-summary','data' => ['report' => $this->report]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.tables.reports.cash-flow-statement-summary'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['report' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->report)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal604c611b10ad2303150a5b3f24357654)): ?>
<?php $attributes = $__attributesOriginal604c611b10ad2303150a5b3f24357654; ?>
<?php unset($__attributesOriginal604c611b10ad2303150a5b3f24357654); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal604c611b10ad2303150a5b3f24357654)): ?>
<?php $component = $__componentOriginal604c611b10ad2303150a5b3f24357654; ?>
<?php unset($__componentOriginal604c611b10ad2303150a5b3f24357654); ?>
<?php endif; ?>
            <?php elseif($activeTab === 'details'): ?>
                <?php if (isset($component)) { $__componentOriginal0578be57e3b09ee57aca95d75a397b2d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0578be57e3b09ee57aca95d75a397b2d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.tables.reports.cash-flow-statement','data' => ['report' => $this->report]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.tables.reports.cash-flow-statement'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['report' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->report)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0578be57e3b09ee57aca95d75a397b2d)): ?>
<?php $attributes = $__attributesOriginal0578be57e3b09ee57aca95d75a397b2d; ?>
<?php unset($__attributesOriginal0578be57e3b09ee57aca95d75a397b2d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0578be57e3b09ee57aca95d75a397b2d)): ?>
<?php $component = $__componentOriginal0578be57e3b09ee57aca95d75a397b2d; ?>
<?php unset($__componentOriginal0578be57e3b09ee57aca95d75a397b2d); ?>
<?php endif; ?>
            <?php endif; ?>
        <?php endif; ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaldce80491e51265a9a2a3daacfd07df81)): ?>
<?php $attributes = $__attributesOriginaldce80491e51265a9a2a3daacfd07df81; ?>
<?php unset($__attributesOriginaldce80491e51265a9a2a3daacfd07df81); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaldce80491e51265a9a2a3daacfd07df81)): ?>
<?php $component = $__componentOriginaldce80491e51265a9a2a3daacfd07df81; ?>
<?php unset($__componentOriginaldce80491e51265a9a2a3daacfd07df81); ?>
<?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>

<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\filament\company\pages\reports\cash-flow-statement.blade.php ENDPATH**/ ?>