<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'actions' => null,
    'actionsVerticalAlignment' => 'center',
    'border' => false,
    'color' => null,
    'description' => null,
    'icon' => null,
    'iconVerticalAlignment' => 'center',
    'iconAnimation' => null,
    'link' => null,
    'linkBlank' => false,
    'linkLabel' => null,
    'title' => null,
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'actions' => null,
    'actionsVerticalAlignment' => 'center',
    'border' => false,
    'color' => null,
    'description' => null,
    'icon' => null,
    'iconVerticalAlignment' => 'center',
    'iconAnimation' => null,
    'link' => null,
    'linkBlank' => false,
    'linkLabel' => null,
    'title' => null,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<?php
    use function Filament\Support\get_color_css_variables;

    $colors = \Illuminate\Support\Arr::toCssStyles([
           get_color_css_variables($color, shades: [50, 100, 400, 500, 700, 800]),
   ]);

    $iconClasses = \Illuminate\Support\Arr::toCssClasses([
        'h-5 w-5 text-custom-400',
        $iconAnimation,
    ]);
?>

<div x-data="{}"
     <?php echo e($attributes->class([
         'filament-simple-alert rounded-md bg-custom-50 p-4 dark:bg-custom-400/10',
         'ring-1 ring-custom-100 dark:ring-custom-500/70' => $border,
     ])); ?>

     style="<?php echo e($colors); ?>">
    <div class="flex gap-3">
        <?php if($icon): ?>
            <div class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                'flex-shrink-0',
                $iconVerticalAlignment === 'start' ? 'self-start' : 'self-center',
            ]); ?>">
                <?php if (isset($component)) { $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.icon','data' => ['icon' => $icon,'class' => $iconClasses]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($icon),'class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($iconClasses)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $attributes = $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $component = $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
            </div>
        <?php endif; ?>
        <div class="items-center flex-1 md:flex md:justify-between space-y-3 md:space-y-0 md:gap-3">
            <?php if($title || $description): ?>
                <div class="space-y-0.5">
                    <?php if($title): ?>
                        <p class="text-sm font-medium text-custom-800 dark:text-white">
                            <?php echo e($title); ?>

                        </p>
                    <?php endif; ?>
                    <?php if($description): ?>
                        <p class="text-sm text-custom-700 dark:text-white">
                            <?php echo e($description); ?>

                        </p>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            <?php if($link || $actions): ?>
                <div class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                  'flex items-center gap-3',
                    $actionsVerticalAlignment === 'start' ? 'self-start' : 'self-center',
                ]); ?>">
                    <div class="flex items-center whitespace-nowrap gap-3">
                        <?php if($link): ?>
                            <p class="text-sm md:mt-0 self-center">
                                <a href="<?php echo e($link); ?>" <?php echo e($linkBlank ? 'target="_blank"' : ''); ?> class="whitespace-nowrap font-medium text-custom-400 hover:text-custom-500">
                                    <?php echo e($linkLabel); ?>

                                    <span aria-hidden="true"> &rarr;</span>
                                </a>
                            </p>
                        <?php endif; ?>
                        <?php if($actions): ?>
                            <div class="gap-3 flex items-center justify-start">
                                <?php $__currentLoopData = $actions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $action): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($action->isVisible()): ?>
                                        <?php echo e($action); ?>

                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\vendor\codewithdennis\filament-simple-alert\resources\views\components\simple-alert.blade.php ENDPATH**/ ?>