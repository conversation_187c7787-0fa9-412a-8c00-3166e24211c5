<?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('heading', null, []); ?> 
        Cash Flow Statement
     <?php $__env->endSlot(); ?>
    
    <?php if(isset($data['error'])): ?>
        <div class="text-center py-8">
            <div class="text-red-500 text-lg font-semibold">Error generating cash flow statement</div>
            <div class="text-gray-600 mt-2"><?php echo e($data['error']); ?></div>
        </div>
    <?php elseif(isset($data['operating_activities']) || isset($data['investing_activities']) || isset($data['financing_activities'])): ?>
        <div class="space-y-6">
            <!-- Operating Activities Section -->
            <?php if(isset($data['operating_activities'])): ?>
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Operating Activities</h3>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm">
                                <thead>
                                    <tr class="border-b border-gray-200 dark:border-gray-700">
                                        <th class="text-left py-2 font-medium text-gray-900 dark:text-white">Description</th>
                                        <th class="text-right py-2 font-medium text-gray-900 dark:text-white">Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $data['operating_activities']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr class="border-b border-gray-100 dark:border-gray-700">
                                            <td class="py-2 text-gray-700 dark:text-gray-300"><?php echo e($activity['description'] ?? 'Unknown'); ?></td>
                                            <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                                <?php echo e(number_format($activity['amount'] ?? 0, 2)); ?>

                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <tr class="border-t-2 border-gray-300 dark:border-gray-600 font-semibold">
                                        <td class="py-2 text-gray-900 dark:text-white">Net Cash from Operating Activities</td>
                                        <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                            <?php echo e(number_format($data['net_operating_cash'] ?? 0, 2)); ?>

                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Investing Activities Section -->
            <?php if(isset($data['investing_activities'])): ?>
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Investing Activities</h3>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm">
                                <thead>
                                    <tr class="border-b border-gray-200 dark:border-gray-700">
                                        <th class="text-left py-2 font-medium text-gray-900 dark:text-white">Description</th>
                                        <th class="text-right py-2 font-medium text-gray-900 dark:text-white">Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $data['investing_activities']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr class="border-b border-gray-100 dark:border-gray-700">
                                            <td class="py-2 text-gray-700 dark:text-gray-300"><?php echo e($activity['description'] ?? 'Unknown'); ?></td>
                                            <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                                <?php echo e(number_format($activity['amount'] ?? 0, 2)); ?>

                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <tr class="border-t-2 border-gray-300 dark:border-gray-600 font-semibold">
                                        <td class="py-2 text-gray-900 dark:text-white">Net Cash from Investing Activities</td>
                                        <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                            <?php echo e(number_format($data['net_investing_cash'] ?? 0, 2)); ?>

                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Financing Activities Section -->
            <?php if(isset($data['financing_activities'])): ?>
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Financing Activities</h3>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm">
                                <thead>
                                    <tr class="border-b border-gray-200 dark:border-gray-700">
                                        <th class="text-left py-2 font-medium text-gray-900 dark:text-white">Description</th>
                                        <th class="text-right py-2 font-medium text-gray-900 dark:text-white">Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $data['financing_activities']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr class="border-b border-gray-100 dark:border-gray-700">
                                            <td class="py-2 text-gray-700 dark:text-gray-300"><?php echo e($activity['description'] ?? 'Unknown'); ?></td>
                                            <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                                <?php echo e(number_format($activity['amount'] ?? 0, 2)); ?>

                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <tr class="border-t-2 border-gray-300 dark:border-gray-600 font-semibold">
                                        <td class="py-2 text-gray-900 dark:text-white">Net Cash from Financing Activities</td>
                                        <td class="py-2 text-right text-gray-900 dark:text-white font-mono">
                                            <?php echo e(number_format($data['net_financing_cash'] ?? 0, 2)); ?>

                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Summary -->
            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 p-6">
                <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4">Cash Flow Summary</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-blue-700 dark:text-blue-300">Net Cash from Operating Activities:</span>
                        <span class="font-mono font-semibold text-blue-900 dark:text-blue-100">
                            <?php echo e(number_format($data['net_operating_cash'] ?? 0, 2)); ?>

                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-blue-700 dark:text-blue-300">Net Cash from Investing Activities:</span>
                        <span class="font-mono font-semibold text-blue-900 dark:text-blue-100">
                            <?php echo e(number_format($data['net_investing_cash'] ?? 0, 2)); ?>

                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-blue-700 dark:text-blue-300">Net Cash from Financing Activities:</span>
                        <span class="font-mono font-semibold text-blue-900 dark:text-blue-100">
                            <?php echo e(number_format($data['net_financing_cash'] ?? 0, 2)); ?>

                        </span>
                    </div>
                    <div class="border-t border-blue-200 dark:border-blue-700 pt-3 flex justify-between">
                        <span class="text-lg font-semibold text-blue-900 dark:text-blue-100">Net Change in Cash:</span>
                        <span class="text-lg font-mono font-bold text-blue-900 dark:text-blue-100">
                            <?php echo e(number_format($data['net_cash_change'] ?? 0, 2)); ?>

                        </span>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="text-center py-8">
            <div class="text-gray-500 text-lg">No cash flow data available for the selected period</div>
        </div>
    <?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\filament\company\reports\cash-flow.blade.php ENDPATH**/ ?>