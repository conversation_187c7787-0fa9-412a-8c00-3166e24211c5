<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'shape' => 'square',
    'size' => 'lg',
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'shape' => 'square',
    'size' => 'lg',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<img <?php echo e($attributes
        ->class([
            'doc-template-logo object-contain',
            match ($size) {
                'sm' => 'max-h-8',
                'md' => 'max-h-16',
                'lg' => 'max-h-24',
                'xl' => 'max-h-32',
                default => $size,
            },
            match ($shape) {
                'square' => 'rounded-none',
                'rounded' => 'rounded-md',
                'circle' => 'rounded-full',
                default => $shape,
            },
        ])); ?>

/>

<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\company\document-template\logo.blade.php ENDPATH**/ ?>