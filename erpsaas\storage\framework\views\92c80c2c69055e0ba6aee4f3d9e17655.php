<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'reportLoaded' => false,
    'summaryData' => [],
    'targetLabel' => null,
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'reportLoaded' => false,
    'summaryData' => [],
    'targetLabel' => null,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<?php
    use App\Utilities\Currency\CurrencyAccessor;
?>

<div>
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
        <?php if($reportLoaded): ?>
            <div class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                'grid grid-cols-1 gap-1 place-content-center items-end text-center max-w-fit mx-auto',
                'md:grid-cols-[repeat(1,minmax(0,1fr)_minmax(0,4rem))_minmax(0,1fr)]' => count($summaryData) === 2,
                'md:grid-cols-[repeat(2,minmax(0,1fr)_minmax(0,4rem))_minmax(0,1fr)]' => count($summaryData) === 3,
                'md:grid-cols-[repeat(3,minmax(0,1fr)_minmax(0,4rem))_minmax(0,1fr)]' => count($summaryData) === 4,
                'md:grid-cols-[repeat(4,minmax(0,1fr)_minmax(0,4rem))_minmax(0,1fr)]' => count($summaryData) === 5,
            ]); ?>">
                <?php $__currentLoopData = $summaryData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $summary): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="text-sm">
                        <div class="text-gray-600 dark:text-gray-200 font-medium mb-2"><?php echo e($summary['label']); ?></div>

                        <?php
                            $isTargetLabel = $summary['label'] === $targetLabel;
                            $isPositive = money($summary['value'], CurrencyAccessor::getDefaultCurrency())->isPositive();
                        ?>

                        <strong
                            class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                                'text-lg',
                                'text-success-700 dark:text-success-400' => $isTargetLabel && $isPositive,
                                'text-danger-700 dark:text-danger-400' => $isTargetLabel && ! $isPositive,
                            ]); ?>"
                        >
                            <?php echo e($summary['value']); ?>

                        </strong>
                    </div>

                    <?php if(! $loop->last): ?>
                        <div class="flex items-center justify-center">
                            <strong class="text-lg">
                                <?php echo e($loop->remaining === 1 ? '=' : '-'); ?>

                            </strong>
                        </div>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php endif; ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
</div>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\report-summary-section.blade.php ENDPATH**/ ?>