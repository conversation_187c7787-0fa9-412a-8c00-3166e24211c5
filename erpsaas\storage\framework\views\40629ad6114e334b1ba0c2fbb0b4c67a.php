<div class="space-y-6">
    <!-- Overview Metrics -->
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('heading', null, []); ?> 
            <div class="flex items-center gap-2">
                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-chart-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                Overview Metrics
            </div>
         <?php $__env->endSlot(); ?>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <div class="text-blue-600 dark:text-blue-400 text-sm font-medium">Total Transactions</div>
                <div class="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    <?php echo e(number_format($data['overview']['total_transactions'])); ?>

                </div>
                <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">
                    <?php echo e(number_format($data['overview']['growth_rates']['transactions'], 1)); ?>% from last period
                </div>
            </div>
            
            <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                <div class="text-green-600 dark:text-green-400 text-sm font-medium">Total Volume</div>
                <div class="text-2xl font-bold text-green-900 dark:text-green-100">
                    <?php echo e(number_format($data['overview']['total_volume'], 2)); ?> TRY
                </div>
                <div class="text-xs text-green-600 dark:text-green-400 mt-1">
                    <?php echo e(number_format($data['overview']['growth_rates']['volume'], 1)); ?>% from last period
                </div>
            </div>
            
            <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                <div class="text-yellow-600 dark:text-yellow-400 text-sm font-medium">Commission Revenue</div>
                <div class="text-2xl font-bold text-yellow-900 dark:text-yellow-100">
                    <?php echo e(number_format($data['overview']['total_commission'], 2)); ?> TRY
                </div>
            </div>
            
            <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                <div class="text-purple-600 dark:text-purple-400 text-sm font-medium">Active Clients</div>
                <div class="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    <?php echo e(number_format($data['overview']['active_clients'])); ?>

                </div>
            </div>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>

    <!-- Transaction Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
             <?php $__env->slot('heading', null, []); ?> Transactions by Type <?php $__env->endSlot(); ?>
            
            <div class="space-y-3">
                <?php $__currentLoopData = $data['transactions']['by_type']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $stats): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div>
                            <div class="font-medium capitalize"><?php echo e(str_replace('_', ' ', $type)); ?></div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                <?php echo e(number_format($stats->count)); ?> transactions
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="font-semibold"><?php echo e(number_format($stats->volume, 2)); ?> TRY</div>
                            <div class="text-sm text-green-600 dark:text-green-400">
                                <?php echo e(number_format($stats->commission, 2)); ?> TRY commission
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>

        <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
             <?php $__env->slot('heading', null, []); ?> Top Currencies <?php $__env->endSlot(); ?>
            
            <div class="space-y-3">
                <?php $__currentLoopData = $data['transactions']['by_currency']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $currency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div>
                            <div class="font-medium"><?php echo e($currency->currency); ?></div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                <?php echo e(number_format($currency->count)); ?> transactions
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="font-semibold"><?php echo e(number_format($currency->volume, 2)); ?></div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
    </div>

    <!-- Client Analytics -->
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('heading', null, []); ?> Client Analytics <?php $__env->endSlot(); ?>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="space-y-4">
                <h4 class="font-semibold text-gray-900 dark:text-gray-100">Client Categories</h4>
                <?php $__currentLoopData = $data['clients']['by_category']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex justify-between items-center">
                        <span class="capitalize"><?php echo e(str_replace('_', ' ', $category)); ?></span>
                        <span class="font-semibold"><?php echo e(number_format($count)); ?></span>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            
            <div class="space-y-4">
                <h4 class="font-semibold text-gray-900 dark:text-gray-100">Risk Levels</h4>
                <?php $__currentLoopData = $data['clients']['by_risk_level']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $risk => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex justify-between items-center">
                        <span class="capitalize"><?php echo e(str_replace('_', ' ', $risk)); ?></span>
                        <span class="font-semibold"><?php echo e(number_format($count)); ?></span>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            
            <div class="space-y-4">
                <h4 class="font-semibold text-gray-900 dark:text-gray-100">Key Metrics</h4>
                <div class="flex justify-between items-center">
                    <span>New Clients</span>
                    <span class="font-semibold text-green-600"><?php echo e(number_format($data['clients']['new_clients'])); ?></span>
                </div>
                <div class="flex justify-between items-center">
                    <span>Retention Rate</span>
                    <span class="font-semibold text-blue-600"><?php echo e(number_format($data['clients']['retention_rate'], 1)); ?>%</span>
                </div>
            </div>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>

    <!-- Financial Summary -->
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('heading', null, []); ?> Financial Summary <?php $__env->endSlot(); ?>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div class="text-green-600 dark:text-green-400 text-sm font-medium">Total Revenue</div>
                <div class="text-xl font-bold text-green-900 dark:text-green-100">
                    <?php echo e(number_format($data['financial']['total_revenue'], 2)); ?> TRY
                </div>
            </div>
            
            <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div class="text-blue-600 dark:text-blue-400 text-sm font-medium">Avg Commission Rate</div>
                <div class="text-xl font-bold text-blue-900 dark:text-blue-100">
                    <?php echo e(number_format($data['financial']['avg_commission_rate'], 2)); ?>%
                </div>
            </div>
            
            <div class="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <div class="text-purple-600 dark:text-purple-400 text-sm font-medium">Completion Rate</div>
                <div class="text-xl font-bold text-purple-900 dark:text-purple-100">
                    <?php echo e(number_format($data['transactions']['completion_rate'], 1)); ?>%
                </div>
            </div>
            
            <div class="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                <div class="text-orange-600 dark:text-orange-400 text-sm font-medium">Avg Transaction</div>
                <div class="text-xl font-bold text-orange-900 dark:text-orange-100">
                    <?php echo e(number_format($data['overview']['avg_transaction_size'], 2)); ?> TRY
                </div>
            </div>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>

    <!-- Operational Metrics -->
    <?php if(isset($data['operational'])): ?>
        <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
             <?php $__env->slot('heading', null, []); ?> Operational Metrics <?php $__env->endSlot(); ?>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="space-y-3">
                    <h4 class="font-semibold text-gray-900 dark:text-gray-100">Delivery Performance</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span>Total Deliveries</span>
                            <span class="font-semibold"><?php echo e(number_format($data['operational']['delivery']['total_deliveries'])); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span>Completed</span>
                            <span class="font-semibold text-green-600"><?php echo e(number_format($data['operational']['delivery']['completed_deliveries'])); ?></span>
                        </div>
                        <?php if($data['operational']['delivery']['avg_delivery_time']): ?>
                            <div class="flex justify-between">
                                <span>Avg Delivery Time</span>
                                <span class="font-semibold"><?php echo e(number_format($data['operational']['delivery']['avg_delivery_time'])); ?> min</span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="space-y-3">
                    <h4 class="font-semibold text-gray-900 dark:text-gray-100">Balance Overview</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span>Total Client Balances</span>
                            <span class="font-semibold"><?php echo e(number_format($data['operational']['balance']['total_client_balances'], 2)); ?> TRY</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Negative Balances</span>
                            <span class="font-semibold text-red-600"><?php echo e(number_format($data['operational']['balance']['negative_balances'])); ?></span>
                        </div>
                    </div>
                </div>
                
                <div class="space-y-3">
                    <h4 class="font-semibold text-gray-900 dark:text-gray-100">Processing Times</h4>
                    <div class="space-y-2">
                        <?php if($data['operational']['processing_times']['avg_processing_time']): ?>
                            <div class="flex justify-between">
                                <span>Average</span>
                                <span class="font-semibold"><?php echo e(number_format($data['operational']['processing_times']['avg_processing_time'])); ?> min</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Fastest</span>
                                <span class="font-semibold text-green-600"><?php echo e(number_format($data['operational']['processing_times']['min_processing_time'])); ?> min</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Slowest</span>
                                <span class="font-semibold text-red-600"><?php echo e(number_format($data['operational']['processing_times']['max_processing_time'])); ?> min</span>
                            </div>
                        <?php else: ?>
                            <div class="text-gray-500 text-sm">No processing time data available</div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
    <?php endif; ?>
</div>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\filament\company\reports\dashboard.blade.php ENDPATH**/ ?>