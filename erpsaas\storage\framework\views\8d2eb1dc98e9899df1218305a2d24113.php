<table class="w-full min-w-[50rem] divide-y divide-gray-200 dark:divide-white/5">
    <colgroup>
        <col span="1" style="width: 65%;">
        <col span="1" style="width: 35%;">
    </colgroup>
    <?php if (isset($component)) { $__componentOriginal088d9df2e25f0de01ebf6280a5631361 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal088d9df2e25f0de01ebf6280a5631361 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.tables.header','data' => ['headers' => $report->getSummaryHeaders(),'alignmentClass' => [$report, 'getAlignmentClass']]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.tables.header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['headers' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($report->getSummaryHeaders()),'alignment-class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([$report, 'getAlignmentClass'])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal088d9df2e25f0de01ebf6280a5631361)): ?>
<?php $attributes = $__attributesOriginal088d9df2e25f0de01ebf6280a5631361; ?>
<?php unset($__attributesOriginal088d9df2e25f0de01ebf6280a5631361); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal088d9df2e25f0de01ebf6280a5631361)): ?>
<?php $component = $__componentOriginal088d9df2e25f0de01ebf6280a5631361; ?>
<?php unset($__componentOriginal088d9df2e25f0de01ebf6280a5631361); ?>
<?php endif; ?>
    <?php $__currentLoopData = $report->getSummaryCategories(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $accountCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <tbody class="divide-y divide-gray-200 whitespace-nowrap dark:divide-white/5">
        <?php if (isset($component)) { $__componentOriginalc599c11141ae3c556abf01b55f88e4b0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc599c11141ae3c556abf01b55f88e4b0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.tables.category-header','data' => ['categoryHeaders' => $accountCategory->header,'alignmentClass' => [$report, 'getAlignmentClass']]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.tables.category-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['category-headers' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($accountCategory->header),'alignment-class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([$report, 'getAlignmentClass'])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc599c11141ae3c556abf01b55f88e4b0)): ?>
<?php $attributes = $__attributesOriginalc599c11141ae3c556abf01b55f88e4b0; ?>
<?php unset($__attributesOriginalc599c11141ae3c556abf01b55f88e4b0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc599c11141ae3c556abf01b55f88e4b0)): ?>
<?php $component = $__componentOriginalc599c11141ae3c556abf01b55f88e4b0; ?>
<?php unset($__componentOriginalc599c11141ae3c556abf01b55f88e4b0); ?>
<?php endif; ?>
        <?php $__currentLoopData = $accountCategory->types; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $accountType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr>
                <?php $__currentLoopData = $accountType->summary; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $accountTypeSummaryIndex => $accountTypeSummaryCell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if (isset($component)) { $__componentOriginal24b82ed7131e46c2ad55192929ed9db8 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal24b82ed7131e46c2ad55192929ed9db8 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.tables.cell','data' => ['alignmentClass' => $report->getAlignmentClass($accountTypeSummaryIndex)]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.tables.cell'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['alignment-class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($report->getAlignmentClass($accountTypeSummaryIndex))]); ?>
                        <?php echo e($accountTypeSummaryCell); ?>

                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal24b82ed7131e46c2ad55192929ed9db8)): ?>
<?php $attributes = $__attributesOriginal24b82ed7131e46c2ad55192929ed9db8; ?>
<?php unset($__attributesOriginal24b82ed7131e46c2ad55192929ed9db8); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal24b82ed7131e46c2ad55192929ed9db8)): ?>
<?php $component = $__componentOriginal24b82ed7131e46c2ad55192929ed9db8; ?>
<?php unset($__componentOriginal24b82ed7131e46c2ad55192929ed9db8); ?>
<?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tr>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <tr>
            <?php $__currentLoopData = $accountCategory->summary; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $accountCategorySummaryIndex => $accountCategorySummaryCell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if (isset($component)) { $__componentOriginal24b82ed7131e46c2ad55192929ed9db8 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal24b82ed7131e46c2ad55192929ed9db8 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.tables.cell','data' => ['alignmentClass' => $report->getAlignmentClass($accountCategorySummaryIndex),'bold' => 'true']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.tables.cell'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['alignment-class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($report->getAlignmentClass($accountCategorySummaryIndex)),'bold' => 'true']); ?>
                    <?php echo e($accountCategorySummaryCell); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal24b82ed7131e46c2ad55192929ed9db8)): ?>
<?php $attributes = $__attributesOriginal24b82ed7131e46c2ad55192929ed9db8; ?>
<?php unset($__attributesOriginal24b82ed7131e46c2ad55192929ed9db8); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal24b82ed7131e46c2ad55192929ed9db8)): ?>
<?php $component = $__componentOriginal24b82ed7131e46c2ad55192929ed9db8; ?>
<?php unset($__componentOriginal24b82ed7131e46c2ad55192929ed9db8); ?>
<?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tr>
        <tr>
            <td colspan="<?php echo e(count($report->getSummaryHeaders())); ?>">
                <div class="min-h-12"></div>
            </td>
        </tr>
        </tbody>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</table>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\company\tables\reports\balance-sheet-summary.blade.php ENDPATH**/ ?>