1756292126O:39:"App\Transformers\AgingReportTransformer":2:{s:9:" * report";O:17:"App\DTO\ReportDTO":10:{s:10:"categories";a:1:{s:8:"Entities";a:0:{}}s:12:"overallTotal";N;s:12:"agingSummary";O:22:"App\DTO\AgingBucketDTO":4:{s:7:"current";s:6:"A$0.00";s:7:"periods";a:4:{s:8:"period_1";s:6:"A$0.00";s:8:"period_2";s:6:"A$0.00";s:8:"period_3";s:6:"A$0.00";s:8:"period_4";s:6:"A$0.00";}s:11:"overPeriods";s:6:"A$0.00";s:5:"total";s:6:"A$0.00";}s:18:"entityBalanceTotal";N;s:21:"overallPaymentMetrics";N;s:6:"fields";a:7:{i:0;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:0;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";E:37:"Filament\Support\Enums\Alignment:Left";s:8:" * label";s:6:"Vendor";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:11:"entity_name";}i:1;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:0;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";E:38:"Filament\Support\Enums\Alignment:Right";s:8:" * label";s:7:"Current";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:7:"current";}i:2;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:0;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";r:38;s:8:" * label";s:7:"1 to 30";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:8:"period_1";}i:3;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:0;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";r:38;s:8:" * label";s:8:"31 to 60";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:8:"period_2";}i:4;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:0;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";r:38;s:8:" * label";s:8:"61 to 90";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:8:"period_3";}i:5;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:0;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";r:38;s:8:" * label";s:7:"Over 90";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:12:"over_periods";}i:6;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:0;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";r:38;s:8:" * label";s:5:"Total";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:5:"total";}}s:10:"reportType";N;s:8:"overview";N;s:9:"startDate";N;s:7:"endDate";O:25:"Illuminate\Support\Carbon":3:{s:4:"date";s:26:"2025-08-27 23:59:59.000000";s:13:"timezone_type";i:3;s:8:"timezone";s:3:"UTC";}}s:51:" App\Transformers\AgingReportTransformer entityType";E:46:"App\Enums\Accounting\DocumentEntityType:Vendor";}