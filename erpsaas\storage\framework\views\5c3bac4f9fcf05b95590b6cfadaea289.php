<table class="w-full min-w-[50rem] divide-y divide-gray-200 dark:divide-white/5">
    <colgroup>
        <?php if(array_key_exists('account_code', $report->getHeaders())): ?>
            <col span="1" style="width: 20%;">
            <col span="1" style="width: 55%;">
            <col span="1" style="width: 25%;">
        <?php else: ?>
            <col span="1" style="width: 65%;">
            <col span="1" style="width: 35%;">
        <?php endif; ?>
    </colgroup>
    <?php if (isset($component)) { $__componentOriginal088d9df2e25f0de01ebf6280a5631361 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal088d9df2e25f0de01ebf6280a5631361 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.tables.header','data' => ['headers' => $report->getHeaders(),'alignmentClass' => [$report, 'getAlignmentClass']]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.tables.header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['headers' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($report->getHeaders()),'alignment-class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([$report, 'getAlignmentClass'])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal088d9df2e25f0de01ebf6280a5631361)): ?>
<?php $attributes = $__attributesOriginal088d9df2e25f0de01ebf6280a5631361; ?>
<?php unset($__attributesOriginal088d9df2e25f0de01ebf6280a5631361); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal088d9df2e25f0de01ebf6280a5631361)): ?>
<?php $component = $__componentOriginal088d9df2e25f0de01ebf6280a5631361; ?>
<?php unset($__componentOriginal088d9df2e25f0de01ebf6280a5631361); ?>
<?php endif; ?>
    <?php $__currentLoopData = $report->getCategories(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $accountCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <tbody class="divide-y divide-gray-200 whitespace-nowrap dark:divide-white/5">
        <?php if (isset($component)) { $__componentOriginalc599c11141ae3c556abf01b55f88e4b0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc599c11141ae3c556abf01b55f88e4b0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.tables.category-header','data' => ['categoryHeaders' => $accountCategory->header,'alignmentClass' => [$report, 'getAlignmentClass']]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.tables.category-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['category-headers' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($accountCategory->header),'alignment-class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([$report, 'getAlignmentClass'])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc599c11141ae3c556abf01b55f88e4b0)): ?>
<?php $attributes = $__attributesOriginalc599c11141ae3c556abf01b55f88e4b0; ?>
<?php unset($__attributesOriginalc599c11141ae3c556abf01b55f88e4b0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc599c11141ae3c556abf01b55f88e4b0)): ?>
<?php $component = $__componentOriginalc599c11141ae3c556abf01b55f88e4b0; ?>
<?php unset($__componentOriginalc599c11141ae3c556abf01b55f88e4b0); ?>
<?php endif; ?>
        <?php $__currentLoopData = $accountCategory->data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $categoryAccount): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr>
                <?php $__currentLoopData = $categoryAccount; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $accountIndex => $categoryAccountCell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if (isset($component)) { $__componentOriginal24b82ed7131e46c2ad55192929ed9db8 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal24b82ed7131e46c2ad55192929ed9db8 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.tables.cell','data' => ['alignmentClass' => $report->getAlignmentClass($accountIndex)]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.tables.cell'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['alignment-class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($report->getAlignmentClass($accountIndex))]); ?>
                        <?php if(is_array($categoryAccountCell) && isset($categoryAccountCell['name'])): ?>
                            <?php if($categoryAccountCell['name'] === 'Retained Earnings' && isset($categoryAccountCell['start_date']) && isset($categoryAccountCell['end_date'])): ?>
                                <?php if (isset($component)) { $__componentOriginal549c94d872270b69c72bdf48cb183bc9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal549c94d872270b69c72bdf48cb183bc9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.link','data' => ['color' => 'primary','target' => '_blank','icon' => 'heroicon-o-arrow-top-right-on-square','iconPosition' => \Filament\Support\Enums\IconPosition::After,'iconSize' => \Filament\Support\Enums\IconSize::Small,'href' => ''.e(\App\Filament\Company\Pages\Reports\IncomeStatement::getUrl([
                                            'startDate' => $categoryAccountCell['start_date'],
                                            'endDate' => $categoryAccountCell['end_date']
                                        ])).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['color' => 'primary','target' => '_blank','icon' => 'heroicon-o-arrow-top-right-on-square','icon-position' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(\Filament\Support\Enums\IconPosition::After),'icon-size' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(\Filament\Support\Enums\IconSize::Small),'href' => ''.e(\App\Filament\Company\Pages\Reports\IncomeStatement::getUrl([
                                            'startDate' => $categoryAccountCell['start_date'],
                                            'endDate' => $categoryAccountCell['end_date']
                                        ])).'']); ?>
                                    <?php echo e($categoryAccountCell['name']); ?>

                                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal549c94d872270b69c72bdf48cb183bc9)): ?>
<?php $attributes = $__attributesOriginal549c94d872270b69c72bdf48cb183bc9; ?>
<?php unset($__attributesOriginal549c94d872270b69c72bdf48cb183bc9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal549c94d872270b69c72bdf48cb183bc9)): ?>
<?php $component = $__componentOriginal549c94d872270b69c72bdf48cb183bc9; ?>
<?php unset($__componentOriginal549c94d872270b69c72bdf48cb183bc9); ?>
<?php endif; ?>
                            <?php elseif(isset($categoryAccountCell['id']) && isset($categoryAccountCell['start_date']) && isset($categoryAccountCell['end_date'])): ?>
                                <?php if (isset($component)) { $__componentOriginal549c94d872270b69c72bdf48cb183bc9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal549c94d872270b69c72bdf48cb183bc9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.link','data' => ['color' => 'primary','target' => '_blank','icon' => 'heroicon-o-arrow-top-right-on-square','iconPosition' => \Filament\Support\Enums\IconPosition::After,'iconSize' => \Filament\Support\Enums\IconSize::Small,'href' => ''.e(\App\Filament\Company\Pages\Reports\AccountTransactions::getUrl([
                                            'startDate' => $categoryAccountCell['start_date'],
                                            'endDate' => $categoryAccountCell['end_date'],
                                            'selectedAccount' => $categoryAccountCell['id']
                                        ])).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['color' => 'primary','target' => '_blank','icon' => 'heroicon-o-arrow-top-right-on-square','icon-position' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(\Filament\Support\Enums\IconPosition::After),'icon-size' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(\Filament\Support\Enums\IconSize::Small),'href' => ''.e(\App\Filament\Company\Pages\Reports\AccountTransactions::getUrl([
                                            'startDate' => $categoryAccountCell['start_date'],
                                            'endDate' => $categoryAccountCell['end_date'],
                                            'selectedAccount' => $categoryAccountCell['id']
                                        ])).'']); ?>
                                    <?php echo e($categoryAccountCell['name']); ?>

                                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal549c94d872270b69c72bdf48cb183bc9)): ?>
<?php $attributes = $__attributesOriginal549c94d872270b69c72bdf48cb183bc9; ?>
<?php unset($__attributesOriginal549c94d872270b69c72bdf48cb183bc9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal549c94d872270b69c72bdf48cb183bc9)): ?>
<?php $component = $__componentOriginal549c94d872270b69c72bdf48cb183bc9; ?>
<?php unset($__componentOriginal549c94d872270b69c72bdf48cb183bc9); ?>
<?php endif; ?>
                            <?php else: ?>
                                <?php echo e($categoryAccountCell['name']); ?>

                            <?php endif; ?>
                        <?php else: ?>
                            <?php echo e($categoryAccountCell); ?>

                        <?php endif; ?>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal24b82ed7131e46c2ad55192929ed9db8)): ?>
<?php $attributes = $__attributesOriginal24b82ed7131e46c2ad55192929ed9db8; ?>
<?php unset($__attributesOriginal24b82ed7131e46c2ad55192929ed9db8); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal24b82ed7131e46c2ad55192929ed9db8)): ?>
<?php $component = $__componentOriginal24b82ed7131e46c2ad55192929ed9db8; ?>
<?php unset($__componentOriginal24b82ed7131e46c2ad55192929ed9db8); ?>
<?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tr>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <tr>
            <?php $__currentLoopData = $accountCategory->summary; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $accountCategorySummaryIndex => $accountCategorySummaryCell): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if (isset($component)) { $__componentOriginal24b82ed7131e46c2ad55192929ed9db8 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal24b82ed7131e46c2ad55192929ed9db8 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.tables.cell','data' => ['alignmentClass' => $report->getAlignmentClass($accountCategorySummaryIndex),'bold' => 'true']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.tables.cell'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['alignment-class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($report->getAlignmentClass($accountCategorySummaryIndex)),'bold' => 'true']); ?>
                    <?php echo e($accountCategorySummaryCell); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal24b82ed7131e46c2ad55192929ed9db8)): ?>
<?php $attributes = $__attributesOriginal24b82ed7131e46c2ad55192929ed9db8; ?>
<?php unset($__attributesOriginal24b82ed7131e46c2ad55192929ed9db8); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal24b82ed7131e46c2ad55192929ed9db8)): ?>
<?php $component = $__componentOriginal24b82ed7131e46c2ad55192929ed9db8; ?>
<?php unset($__componentOriginal24b82ed7131e46c2ad55192929ed9db8); ?>
<?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tr>
        <tr>
            <td colspan="<?php echo e(count($report->getHeaders())); ?>">
                <div class="min-h-12"></div>
            </td>
        </tr>
        </tbody>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php if (isset($component)) { $__componentOriginal2eba023e47eb1bdadd50ce574b3e9d81 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2eba023e47eb1bdadd50ce574b3e9d81 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.company.tables.footer','data' => ['totals' => $report->getOverallTotals(),'alignmentClass' => [$report, 'getAlignmentClass']]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('company.tables.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['totals' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($report->getOverallTotals()),'alignment-class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([$report, 'getAlignmentClass'])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2eba023e47eb1bdadd50ce574b3e9d81)): ?>
<?php $attributes = $__attributesOriginal2eba023e47eb1bdadd50ce574b3e9d81; ?>
<?php unset($__attributesOriginal2eba023e47eb1bdadd50ce574b3e9d81); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2eba023e47eb1bdadd50ce574b3e9d81)): ?>
<?php $component = $__componentOriginal2eba023e47eb1bdadd50ce574b3e9d81; ?>
<?php unset($__componentOriginal2eba023e47eb1bdadd50ce574b3e9d81); ?>
<?php endif; ?>
</table>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\components\company\tables\reports\income-statement.blade.php ENDPATH**/ ?>