<?php
    $id = $getId();
    $statePath = $getStatePath();
    $isDisabled = $isDisabled();
    $options = $getOptions();
    $offColor = $getOffColor() ?? 'gray';
    $onColor = $getOnColor() ?? 'primary';
    $gridDirection = $getGridDirection() ?? 'column';
    $icons = $getIcons();
    $iconPosition = $getIconPosition();
    $iconSize = $getIconSize();
?>
<?php if (isset($component)) { $__componentOriginal511d4862ff04963c3c16115c05a86a9d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal511d4862ff04963c3c16115c05a86a9d = $attributes; } ?>
<?php $component = Illuminate\View\DynamicComponent::resolve(['component' => $getFieldWrapperView()] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dynamic-component'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\DynamicComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['field' => $field]); ?>
    <div
        class="<?php echo \Illuminate\Support\Arr::toCssClasses([
            'selectify-button-group-grid grid gap-3',
            'grid-cols-1 md:grid-cols-2' => $gridDirection === 'row',
            'sm:grid-flow-col sm:grid-rows-2' => $gridDirection === 'column',
        ]); ?>"
        x-data="{
            state: $wire.<?php echo e($applyStateBindingModifiers("entangle('{$statePath}')")); ?>,
            getColor(color) {
                return color === 'gray'
                    ? 'bg-white text-gray-950 hover:bg-gray-50 dark:bg-white/5 dark:text-white dark:hover:bg-white/10 ring-1 ring-gray-950/10 dark:ring-white/20'
                    : 'bg-custom-600 text-white hover:bg-custom-500 dark:bg-custom-500 dark:hover:bg-custom-400 focus:ring-custom-500/50 dark:focus:ring-custom-400/50';
            }
        }"
    >
        <?php $__currentLoopData = $options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
                $inputId = "{$id}-{$value}";
                $shouldOptionBeDisabled = $isDisabled || $isOptionDisabled($value, $label);
            ?>

            <label
                for="<?php echo e($inputId); ?>"
                x-on:click="state = '<?php echo e($value); ?>'"
                x-bind:class="
                    state == '<?php echo e($value); ?>'
                        ? getColor('<?php echo e($onColor); ?>')
                        : getColor('<?php echo e($offColor); ?>')
                "
                x-bind:style="
                    state == '<?php echo e($value); ?>'
                        ? '<?php echo e(\Filament\Support\get_color_css_variables($onColor, shades: [600, 500, 400])); ?>'
                        : '<?php echo e(\Filament\Support\get_color_css_variables($offColor, shades: [600, 500, 400])); ?>'
                "
                <?php echo e($attributes
                    ->merge($getExtraAttributes(), escape: false)
                    ->merge($getExtraAlpineAttributes(), escape: false)
                    ->class([
                        'selectify-button-group items-center justify-center font-semibold outline-none transition duration-75 focus:ring-2 rounded-lg gap-1.5 px-3 py-2 text-sm flex shadow-sm hover:cursor-pointer',
                        'opacity-70 pointer-events-none' => $shouldOptionBeDisabled,
                        'flex-row mr-1' => $iconPosition === \Filament\Support\Enums\IconPosition::Before || $iconPosition === 'before',
                        'flex-row-reverse ml-1' => $iconPosition === \Filament\Support\Enums\IconPosition::After || $iconPosition === 'after',
                    ])); ?>

                <?php echo e($getExtraAlpineAttributeBag()); ?>

            >
                <input
                    type="radio"
                    name="<?php echo e($id); ?>"
                    id="<?php echo e($inputId); ?>"
                    value="<?php echo e($value); ?>"
                    class="sr-only"
                    aria-labelledby="<?php echo e($inputId); ?>"
                    <?php if($shouldOptionBeDisabled): echo 'disabled'; endif; ?>
                    wire:loading.attr="disabled"
                />
                <?php if(filled($icons)): ?>
                    <?php if (isset($component)) { $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.icon','data' => ['icon' => ''.e($icons[$value]).'','class' => \Illuminate\Support\Arr::toCssClasses([
                            match ($iconSize) {
                                \Filament\Support\Enums\IconSize::Small, 'sm' => 'h-4 w-4 mt-1',
                                \Filament\Support\Enums\IconSize::Medium, 'md' => 'h-5 w-5 mt-0.5',
                                \Filament\Support\Enums\IconSize::Large, 'lg' => 'h-6 w-6',
                                default => $iconSize,
                            },
                        ])]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => ''.e($icons[$value]).'','class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(\Illuminate\Support\Arr::toCssClasses([
                            match ($iconSize) {
                                \Filament\Support\Enums\IconSize::Small, 'sm' => 'h-4 w-4 mt-1',
                                \Filament\Support\Enums\IconSize::Medium, 'md' => 'h-5 w-5 mt-0.5',
                                \Filament\Support\Enums\IconSize::Large, 'lg' => 'h-6 w-6',
                                default => $iconSize,
                            },
                        ]))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $attributes = $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $component = $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
                <?php endif; ?>
                <?php echo e($label); ?>

            </label>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal511d4862ff04963c3c16115c05a86a9d)): ?>
<?php $attributes = $__attributesOriginal511d4862ff04963c3c16115c05a86a9d; ?>
<?php unset($__attributesOriginal511d4862ff04963c3c16115c05a86a9d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal511d4862ff04963c3c16115c05a86a9d)): ?>
<?php $component = $__componentOriginal511d4862ff04963c3c16115c05a86a9d; ?>
<?php unset($__componentOriginal511d4862ff04963c3c16115c05a86a9d); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\vendor\andrewdwallo\filament-selectify\resources\views\components\button-group.blade.php ENDPATH**/ ?>