1756292116O:46:"App\Transformers\BalanceSheetReportTransformer":4:{s:9:" * report";O:17:"App\DTO\ReportDTO":10:{s:10:"categories";a:3:{s:6:"Assets";O:26:"App\DTO\AccountCategoryDTO":3:{s:8:"accounts";a:0:{}s:5:"types";a:0:{}s:7:"summary";O:25:"App\DTO\AccountBalanceDTO":5:{s:15:"startingBalance";N;s:12:"debitBalance";N;s:13:"creditBalance";N;s:11:"netMovement";N;s:13:"endingBalance";s:6:"A$0.00";}}s:11:"Liabilities";O:26:"App\DTO\AccountCategoryDTO":3:{s:8:"accounts";a:0:{}s:5:"types";a:0:{}s:7:"summary";O:25:"App\DTO\AccountBalanceDTO":5:{s:15:"startingBalance";N;s:12:"debitBalance";N;s:13:"creditBalance";N;s:11:"netMovement";N;s:13:"endingBalance";s:6:"A$0.00";}}s:6:"Equity";O:26:"App\DTO\AccountCategoryDTO":3:{s:8:"accounts";a:1:{i:0;O:18:"App\DTO\AccountDTO":6:{s:11:"accountName";s:17:"Retained Earnings";s:11:"accountCode";s:2:"RE";s:9:"accountId";N;s:7:"balance";O:25:"App\DTO\AccountBalanceDTO":5:{s:15:"startingBalance";N;s:12:"debitBalance";N;s:13:"creditBalance";N;s:11:"netMovement";N;s:13:"endingBalance";s:6:"A$0.00";}s:9:"startDate";s:10:"2025-08-27";s:7:"endDate";s:10:"2025-08-27";}}s:5:"types";a:0:{}s:7:"summary";O:25:"App\DTO\AccountBalanceDTO":5:{s:15:"startingBalance";N;s:12:"debitBalance";N;s:13:"creditBalance";N;s:11:"netMovement";N;s:13:"endingBalance";s:6:"A$0.00";}}}s:12:"overallTotal";O:25:"App\DTO\AccountBalanceDTO":5:{s:15:"startingBalance";N;s:12:"debitBalance";N;s:13:"creditBalance";N;s:11:"netMovement";N;s:13:"endingBalance";s:6:"A$0.00";}s:12:"agingSummary";N;s:18:"entityBalanceTotal";N;s:21:"overallPaymentMetrics";N;s:6:"fields";a:2:{i:0;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:0;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";E:37:"Filament\Support\Enums\Alignment:Left";s:8:" * label";s:8:"ACCOUNTS";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:12:"account_name";}i:1;O:18:"App\Support\Column":11:{s:9:" * isDate";b:0;s:11:" * isHidden";b:0;s:12:" * isVisible";b:1;s:13:" * hiddenFrom";N;s:14:" * visibleFrom";N;s:15:" * isToggleable";b:0;s:27:" * isToggledHiddenByDefault";b:0;s:12:" * alignment";E:38:"Filament\Support\Enums\Alignment:Right";s:8:" * label";s:12:"Aug 27, 2025";s:23:" * shouldTranslateLabel";b:0;s:7:" * name";s:14:"ending_balance";}}s:10:"reportType";N;s:8:"overview";N;s:9:"startDate";O:25:"Illuminate\Support\Carbon":3:{s:4:"date";s:26:"2025-08-27 00:00:00.000000";s:13:"timezone_type";i:3;s:8:"timezone";s:3:"UTC";}s:7:"endDate";O:25:"Illuminate\Support\Carbon":3:{s:4:"date";s:26:"2025-08-27 23:59:59.000000";s:13:"timezone_type";i:3;s:8:"timezone";s:3:"UTC";}}s:14:" * totalAssets";s:6:"A$0.00";s:19:" * totalLiabilities";s:6:"A$0.00";s:14:" * totalEquity";s:6:"A$0.00";}