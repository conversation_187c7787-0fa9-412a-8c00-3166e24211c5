<?php
    $isDisabled = $action->isDisabled();
?>

<span
    x-data="{}"
    x-tooltip="{
        content: <?php echo \Illuminate\Support\Js::from($action->getTooltip())->toHtml() ?>,
        theme: $store.theme === 'dark' ? 'light' : 'dark',
        placement: 'left',
    }"
>
    <?php if (isset($component)) { $__componentOriginalf0029cce6d19fd6d472097ff06a800a1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf0029cce6d19fd6d472097ff06a800a1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.icon-button','data' => ['class' => \Illuminate\Support\Arr::toCssClasses([
            'disabled:cursor-not-allowed disabled:pointer-events-auto disabled:hover:text-gray-400',
        ]),'icon' => $action->getIcon(),'color' => $action->getColor(),'disabled' => $isDisabled,'size' => $action->getIconSize(),'wire:click' => $action->getLivewireClickHandler()]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::icon-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(\Illuminate\Support\Arr::toCssClasses([
            'disabled:cursor-not-allowed disabled:pointer-events-auto disabled:hover:text-gray-400',
        ])),'icon' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($action->getIcon()),'color' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($action->getColor()),'disabled' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($isDisabled),'size' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($action->getIconSize()),'wire:click' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($action->getLivewireClickHandler())]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf0029cce6d19fd6d472097ff06a800a1)): ?>
<?php $attributes = $__attributesOriginalf0029cce6d19fd6d472097ff06a800a1; ?>
<?php unset($__attributesOriginalf0029cce6d19fd6d472097ff06a800a1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf0029cce6d19fd6d472097ff06a800a1)): ?>
<?php $component = $__componentOriginalf0029cce6d19fd6d472097ff06a800a1; ?>
<?php unset($__componentOriginalf0029cce6d19fd6d472097ff06a800a1); ?>
<?php endif; ?>
</span>
<?php /**PATH C:\Users\<USER>\Documents\arenadoviz\erpsaas\resources\views\filament\company\components\tables\actions\mark-as-reviewed.blade.php ENDPATH**/ ?>